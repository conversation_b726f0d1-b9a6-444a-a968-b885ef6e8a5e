{"name": "@react-google-maps/infobox", "sideEffects": false, "version": "2.20.0", "description": "InfoBox for React.js Google Maps API", "license": "MIT", "type": "module", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JustFly1984"}, "contributors": ["<PERSON> <<EMAIL>> (https://github.com/thekevinbrown)"], "repository": {"type": "git", "url": "https://github.com/JustFly1984/react-google-maps-api.git", "directory": "packages/react-google-maps-api-infobox"}, "bugs": {"url": "https://github.com/JustFly1984/react-google-maps-api/issues"}, "homepage": "https://react-google-maps-api-docs.netlify.app", "publishConfig": {"access": "public"}, "main": "dist/cjs.js", "types": "dist/index.d.ts", "unpkg": "dist/umd.min.js", "module": "dist/esm.js", "files": ["src/", "dist/"], "keywords": ["React", "Google", "Map", "API", "addons/InfoBox"], "devDependencies": {"@rollup/plugin-commonjs": "28.0.0", "@rollup/plugin-node-resolve": "15.3.0", "@rollup/plugin-typescript": "12.1.0", "jest": "29.7.0", "jest-cli": "29.7.0", "rimraf": "6.0.1", "rollup": "4.24.0", "rollup-plugin-dts": "6.1.1", "@rollup/plugin-terser": "0.4.4"}, "gitHead": "80167ddcc3d8e356dbf0b0c3a6292c6a3a989f83", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup --config", "clean": "rimraf ./package-lock.json ./pnpm-lock.yml ./node_modules/ && pnpm install", "lint": "pnpx eslint ./src/**/*.{ts,tsx}", "pub": "pnpm publish .", "tc": "tsc -p ./tsconfig.json --noEmit --traceResolution"}}