<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="76484095-3584-4c3d-b373-e79d00c23f60" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/controller/AuthService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/controller/AuthServiceWrapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/controller/DashboardController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/deporsm/controller/DashboardController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/controller/LogActividadController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/model/LogActividad.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/model/Usuario.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/deporsm/model/Usuario.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/repository/LogActividadRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/repository/UsuarioRepository.java.new" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/deporsm/service/LogActividadService.java" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "JJOB73"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/leoleonar071pucp/Backend-DeporSM-leo",
    "accountId": "8a0a7a37-648d-43a3-9622-48532d236956"
  }
}]]></component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="30C7QUOk6s5XVrnJLzzz9QhBSsp" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.DeporsmApplication.executor": "Run",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="DeporsmApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="deporsm" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.deporsm.DeporsmApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26574.91" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26574.91" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="76484095-3584-4c3d-b373-e79d00c23f60" name="Changes" comment="" />
      <created>1753119689670</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753119689670</updated>
      <workItem from="1753119691854" duration="2281000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>