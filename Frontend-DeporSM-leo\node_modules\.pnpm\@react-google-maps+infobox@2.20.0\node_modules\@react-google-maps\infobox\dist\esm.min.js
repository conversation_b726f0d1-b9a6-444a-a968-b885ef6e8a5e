function t(t){t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation()}var i=function(){function i(t){void 0===t&&(t={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(i,google.maps.OverlayView),this.content=t.content||"",this.disableAutoPan=t.disableAutoPan||!1,this.maxWidth=t.maxWidth||0,this.pixelOffset=t.pixelOffset||new google.maps.Size(0,0),this.position=t.position||new google.maps.LatLng(0,0),this.zIndex=t.zIndex||null,this.boxClass=t.boxClass||"infoBox",this.boxStyle=t.boxStyle||{},this.closeBoxMargin=t.closeBoxMargin||"2px",this.closeBoxURL=t.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",""===t.closeBoxURL&&(this.closeBoxURL=""),this.infoBoxClearance=t.infoBoxClearance||new google.maps.Size(1,1),void 0===t.visible&&(void 0===t.isHidden?t.visible=!0:t.visible=!t.isHidden),this.isHidden=!t.visible,this.alignBottom=t.alignBottom||!1,this.pane=t.pane||"floatPane",this.enableEventPropagation=t.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return i.prototype.createInfoBoxDiv=function(){var i=this;if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),"string"==typeof this.content?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var e=this.getPanes();if(null!==e&&e[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(0!==this.maxWidth&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var s=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-s.left-s.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var o=0,n=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"];o<n.length;o++){var h=n[o];this.eventListeners.push(google.maps.event.addListener(this.div,h,t))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",(function(){i.div&&(i.div.style.cursor="default")})))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",(function(e){e.returnValue=!1,e.preventDefault&&e.preventDefault(),i.enableEventPropagation||t(e)})),google.maps.event.trigger(this,"domready")}},i.prototype.getCloseBoxImg=function(){var t="";return""!==this.closeBoxURL&&(t='<img alt=""',t+=' aria-hidden="true"',t+=" src='"+this.closeBoxURL+"'",t+=" align=right",t+=" style='",t+=" position: relative;",t+=" cursor: pointer;",t+=" margin: "+this.closeBoxMargin+";",t+="'>"),t},i.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&""!==this.closeBoxURL?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},i.prototype.closeClickHandler=function(t){t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},i.prototype.getCloseClickHandler=function(){return this.closeClickHandler},i.prototype.panBox=function(t){if(this.div&&!t){var i=this.getMap();if(i instanceof google.maps.Map){var e=0,s=0,o=i.getBounds();o&&!o.contains(this.position)&&i.setCenter(this.position);var n=i.getDiv(),h=n.offsetWidth,d=n.offsetHeight,l=this.pixelOffset.width,r=this.pixelOffset.height,a=this.div.offsetWidth,p=this.div.offsetHeight,v=this.infoBoxClearance.width,g=this.infoBoxClearance.height,c=this.getProjection().fromLatLngToContainerPixel(this.position);null!==c&&(c.x<-l+v?e=c.x+l-v:c.x+a+l+v>h&&(e=c.x+a+l+v-h),this.alignBottom?c.y<-r+g+p?s=c.y+r-g-p:c.y+r+g>d&&(s=c.y+r+g-d):c.y<-r+g?s=c.y+r-g:c.y+p+r+g>d&&(s=c.y+p+r+g-d)),0===e&&0===s||i.panBy(e,s)}}},i.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var t=this.boxStyle;for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(this.div.style[i]=t[i]);if(this.div.style.webkitTransform="translateZ(0)",void 0!==this.div.style.opacity&&""!==this.div.style.opacity){var e=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+100*e+')"',this.div.style.filter="alpha(opacity="+100*e+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",null!==this.zIndex&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},i.prototype.getBoxWidths=function(){var t={top:0,bottom:0,left:0,right:0};if(!this.div)return t;if(document.defaultView){var i=this.div.ownerDocument,e=i&&i.defaultView?i.defaultView.getComputedStyle(this.div,""):null;e&&(t.top=parseInt(e.borderTopWidth||"",10)||0,t.bottom=parseInt(e.borderBottomWidth||"",10)||0,t.left=parseInt(e.borderLeftWidth||"",10)||0,t.right=parseInt(e.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var s=this.div.currentStyle;s&&(t.top=parseInt(s.borderTopWidth||"",10)||0,t.bottom=parseInt(s.borderBottomWidth||"",10)||0,t.left=parseInt(s.borderLeftWidth||"",10)||0,t.right=parseInt(s.borderRightWidth||"",10)||0)}return t},i.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},i.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var t=this.getProjection().fromLatLngToDivPixel(this.position);null!==t&&(this.div.style.left=t.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(t.y+this.pixelOffset.height)+"px":this.div.style.top=t.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},i.prototype.setOptions=function(t){void 0===t&&(t={}),void 0!==t.boxClass&&(this.boxClass=t.boxClass,this.setBoxStyle()),void 0!==t.boxStyle&&(this.boxStyle=t.boxStyle,this.setBoxStyle()),void 0!==t.content&&this.setContent(t.content),void 0!==t.disableAutoPan&&(this.disableAutoPan=t.disableAutoPan),void 0!==t.maxWidth&&(this.maxWidth=t.maxWidth),void 0!==t.pixelOffset&&(this.pixelOffset=t.pixelOffset),void 0!==t.alignBottom&&(this.alignBottom=t.alignBottom),void 0!==t.position&&this.setPosition(t.position),void 0!==t.zIndex&&this.setZIndex(t.zIndex),void 0!==t.closeBoxMargin&&(this.closeBoxMargin=t.closeBoxMargin),void 0!==t.closeBoxURL&&(this.closeBoxURL=t.closeBoxURL),void 0!==t.infoBoxClearance&&(this.infoBoxClearance=t.infoBoxClearance),void 0!==t.isHidden&&(this.isHidden=t.isHidden),void 0!==t.visible&&(this.isHidden=!t.visible),void 0!==t.enableEventPropagation&&(this.enableEventPropagation=t.enableEventPropagation),this.div&&this.draw()},i.prototype.setContent=function(t){this.content=t,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),"string"==typeof t?this.div.innerHTML=this.getCloseBoxImg()+t:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(t)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px","string"==typeof t?this.div.innerHTML=this.getCloseBoxImg()+t:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(t))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},i.prototype.setPosition=function(t){this.position=t,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},i.prototype.setVisible=function(t){this.isHidden=!t,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},i.prototype.setZIndex=function(t){this.zIndex=t,this.div&&(this.div.style.zIndex=t+""),google.maps.event.trigger(this,"zindex_changed")},i.prototype.getContent=function(){return this.content},i.prototype.getPosition=function(){return this.position},i.prototype.getZIndex=function(){return this.zIndex},i.prototype.getVisible=function(){var t=this.getMap();return null!=t&&!this.isHidden},i.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},i.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},i.prototype.open=function(t,i){var e=this;i&&(this.position=i.getPosition(),this.moveListener=google.maps.event.addListener(i,"position_changed",(function(){var t=i.getPosition();e.setPosition(t)})),this.mapListener=google.maps.event.addListener(i,"map_changed",(function(){e.setMap(i.map)}))),this.setMap(t),this.div&&this.panBox()},i.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var t=0,i=this.eventListeners;t<i.length;t++){var e=i[t];google.maps.event.removeListener(e)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},i.prototype.extend=function(t,i){return function(t){for(var i in t.prototype)Object.prototype.hasOwnProperty.call(this,i)||(this.prototype[i]=t.prototype[i]);return this}.apply(t,[i])},i}();export{i as InfoBox};
//# sourceMappingURL=esm.min.js.map
