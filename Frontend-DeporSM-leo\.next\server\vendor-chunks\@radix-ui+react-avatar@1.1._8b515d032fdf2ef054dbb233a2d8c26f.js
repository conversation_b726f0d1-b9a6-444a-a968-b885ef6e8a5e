"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-avatar@1.1._8b515d032fdf2ef054dbb233a2d8c26f";
exports.ids = ["vendor-chunks/@radix-ui+react-avatar@1.1._8b515d032fdf2ef054dbb233a2d8c26f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._8b515d032fdf2ef054dbb233a2d8c26f/node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-avatar@1.1._8b515d032fdf2ef054dbb233a2d8c26f/node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_cc1ff12cc9bbe51d2500ecb6a8f5f40c/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_0411fda863efbb3d1d9fe16b480328a5/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_a037b35919e7812e7fbf25b9855b76e3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_810a47b3d5d4a3e8a8b93692c4f23eb0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // packages/react/avatar/src/avatar.tsx\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)({\n        \"AvatarImage.useCallbackRef[handleLoadingStatusChange]\": (status)=>{\n            onLoadingStatusChange(status);\n            context.onImageLoadingStatusChange(status);\n        }\n    }[\"AvatarImage.useCallbackRef[handleLoadingStatusChange]\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"AvatarImage.useLayoutEffect\": ()=>{\n            if (imageLoadingStatus !== \"idle\") {\n                handleLoadingStatusChange(imageLoadingStatus);\n            }\n        }\n    }[\"AvatarImage.useLayoutEffect\"], [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"AvatarFallback.useEffect\": ()=>{\n            if (delayMs !== void 0) {\n                const timerId = window.setTimeout({\n                    \"AvatarFallback.useEffect.timerId\": ()=>setCanRender(true)\n                }[\"AvatarFallback.useEffect.timerId\"], delayMs);\n                return ({\n                    \"AvatarFallback.useEffect\": ()=>window.clearTimeout(timerId)\n                })[\"AvatarFallback.useEffect\"];\n            }\n        }\n    }[\"AvatarFallback.useEffect\"], [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction useImageLoadingStatus(src, referrerPolicy) {\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            if (!src) {\n                setLoadingStatus(\"error\");\n                return;\n            }\n            let isMounted = true;\n            const image = new window.Image();\n            const updateStatus = {\n                \"useImageLoadingStatus.useLayoutEffect.updateStatus\": (status)=>({\n                        \"useImageLoadingStatus.useLayoutEffect.updateStatus\": ()=>{\n                            if (!isMounted) return;\n                            setLoadingStatus(status);\n                        }\n                    })[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"]\n            }[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"];\n            setLoadingStatus(\"loading\");\n            image.onload = updateStatus(\"loaded\");\n            image.onerror = updateStatus(\"error\");\n            image.src = src;\n            if (referrerPolicy) {\n                image.referrerPolicy = referrerPolicy;\n            }\n            return ({\n                \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"useImageLoadingStatus.useLayoutEffect\"];\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        src,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._8b515d032fdf2ef054dbb233a2d8c26f/node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ })

};
;