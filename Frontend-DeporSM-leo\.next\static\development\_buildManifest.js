self.__BUILD_MANIFEST = (function(a){return {__rewrites:{afterFiles:[{has:a,source:"\u002Fapi\u002Finstalaciones\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Finstalaciones",destination:a},{has:a,source:"\u002Fapi\u002Fmantenimientos\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Fmantenimientos",destination:a},{has:a,source:"\u002Fapi\u002Fusuarios\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Fusuarios",destination:a},{has:a,source:"\u002Fapi\u002Freservas\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Freservas",destination:a},{has:a,source:"\u002Fapi\u002Fauth\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Fauth",destination:a},{has:a,source:"\u002Fapi\u002Freportes\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Freportes",destination:a},{has:a,source:"\u002Fapi\u002Fadmin\u002F:path*",destination:a},{has:a,source:"\u002Fapi\u002Fadmin",destination:a}],beforeFiles:[],fallback:[]},__routerFilterStatic:a,__routerFilterDynamic:a,sortedPages:["\u002F_app"]}}(void 0));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()