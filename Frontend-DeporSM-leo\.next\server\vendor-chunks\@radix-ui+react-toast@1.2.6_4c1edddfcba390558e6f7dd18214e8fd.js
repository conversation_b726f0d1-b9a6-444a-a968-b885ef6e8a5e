"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-toast@1.2.6_4c1edddfcba390558e6f7dd18214e8fd";
exports.ids = ["vendor-chunks/@radix-ui+react-toast@1.2.6_4c1edddfcba390558e6f7dd18214e8fd"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.6_4c1edddfcba390558e6f7dd18214e8fd/node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-toast@1.2.6_4c1edddfcba390558e6f7dd18214e8fd/node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_e1e01a3816d8e0cf001290f8b282a1d8/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_37e7d2ea4290d1d8a7809ccf28fe8e86/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_cc1ff12cc9bbe51d2500ecb6a8f5f40c/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_82e83d9398e45695d1098cfd87388d76/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._545f0a12f288cb6edce6badd2f3e2422/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._d667c75615b86bf4a73b7a403dbd4be8/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_810a47b3d5d4a3e8a8b93692c4f23eb0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_0411fda863efbb3d1d9fe16b480328a5/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_0b836f13b94dd7033abaf1082aa4f642/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_a037b35919e7812e7fbf25b9855b76e3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_85556fb9479f44884ee6e44f31f1dbfb/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // packages/react/toast/src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"ToastProvider.useCallback\": ()=>setToastCount({\n                        \"ToastProvider.useCallback\": (prevCount)=>prevCount + 1\n                    }[\"ToastProvider.useCallback\"])\n            }[\"ToastProvider.useCallback\"], []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"ToastProvider.useCallback\": ()=>setToastCount({\n                        \"ToastProvider.useCallback\": (prevCount)=>prevCount - 1\n                    }[\"ToastProvider.useCallback\"])\n            }[\"ToastProvider.useCallback\"], []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"ToastViewport.useEffect.handleKeyDown\": (event)=>{\n                    const isHotkeyPressed = hotkey.length !== 0 && hotkey.every({\n                        \"ToastViewport.useEffect.handleKeyDown\": (key)=>event[key] || event.code === key\n                    }[\"ToastViewport.useEffect.handleKeyDown\"]);\n                    if (isHotkeyPressed) ref.current?.focus();\n                }\n            }[\"ToastViewport.useEffect.handleKeyDown\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"ToastViewport.useEffect\": ()=>document.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"ToastViewport.useEffect\"];\n        }\n    }[\"ToastViewport.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const wrapper = wrapperRef.current;\n            const viewport = ref.current;\n            if (hasToasts && wrapper && viewport) {\n                const handlePause = {\n                    \"ToastViewport.useEffect.handlePause\": ()=>{\n                        if (!context.isClosePausedRef.current) {\n                            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                            viewport.dispatchEvent(pauseEvent);\n                            context.isClosePausedRef.current = true;\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handlePause\"];\n                const handleResume = {\n                    \"ToastViewport.useEffect.handleResume\": ()=>{\n                        if (context.isClosePausedRef.current) {\n                            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                            viewport.dispatchEvent(resumeEvent);\n                            context.isClosePausedRef.current = false;\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handleResume\"];\n                const handleFocusOutResume = {\n                    \"ToastViewport.useEffect.handleFocusOutResume\": (event)=>{\n                        const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                        if (isFocusMovingOutside) handleResume();\n                    }\n                }[\"ToastViewport.useEffect.handleFocusOutResume\"];\n                const handlePointerLeaveResume = {\n                    \"ToastViewport.useEffect.handlePointerLeaveResume\": ()=>{\n                        const isFocusInside = wrapper.contains(document.activeElement);\n                        if (!isFocusInside) handleResume();\n                    }\n                }[\"ToastViewport.useEffect.handlePointerLeaveResume\"];\n                wrapper.addEventListener(\"focusin\", handlePause);\n                wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.addEventListener(\"pointermove\", handlePause);\n                wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.addEventListener(\"blur\", handlePause);\n                window.addEventListener(\"focus\", handleResume);\n                return ({\n                    \"ToastViewport.useEffect\": ()=>{\n                        wrapper.removeEventListener(\"focusin\", handlePause);\n                        wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                        wrapper.removeEventListener(\"pointermove\", handlePause);\n                        wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                        window.removeEventListener(\"blur\", handlePause);\n                        window.removeEventListener(\"focus\", handleResume);\n                    }\n                })[\"ToastViewport.useEffect\"];\n            }\n        }\n    }[\"ToastViewport.useEffect\"], [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ToastViewport.useCallback[getSortedTabbableCandidates]\": ({ tabbingDirection })=>{\n            const toastItems = getItems();\n            const tabbableCandidates = toastItems.map({\n                \"ToastViewport.useCallback[getSortedTabbableCandidates].tabbableCandidates\": (toastItem)=>{\n                    const toastNode = toastItem.ref.current;\n                    const toastTabbableCandidates = [\n                        toastNode,\n                        ...getTabbableCandidates(toastNode)\n                    ];\n                    return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n                }\n            }[\"ToastViewport.useCallback[getSortedTabbableCandidates].tabbableCandidates\"]);\n            return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n        }\n    }[\"ToastViewport.useCallback[getSortedTabbableCandidates]\"], [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const viewport = ref.current;\n            if (viewport) {\n                const handleKeyDown = {\n                    \"ToastViewport.useEffect.handleKeyDown\": (event)=>{\n                        const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                        const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                        if (isTabKey) {\n                            const focusedElement = document.activeElement;\n                            const isTabbingBackwards = event.shiftKey;\n                            const targetIsViewport = event.target === viewport;\n                            if (targetIsViewport && isTabbingBackwards) {\n                                headFocusProxyRef.current?.focus();\n                                return;\n                            }\n                            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                            const sortedCandidates = getSortedTabbableCandidates({\n                                tabbingDirection\n                            });\n                            const index = sortedCandidates.findIndex({\n                                \"ToastViewport.useEffect.handleKeyDown.index\": (candidate)=>candidate === focusedElement\n                            }[\"ToastViewport.useEffect.handleKeyDown.index\"]);\n                            if (focusFirst(sortedCandidates.slice(index + 1))) {\n                                event.preventDefault();\n                            } else {\n                                isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                            }\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handleKeyDown\"];\n                viewport.addEventListener(\"keydown\", handleKeyDown);\n                return ({\n                    \"ToastViewport.useEffect\": ()=>viewport.removeEventListener(\"keydown\", handleKeyDown)\n                })[\"ToastViewport.useEffect\"];\n            }\n        }\n    }[\"ToastViewport.useEffect\"], [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open = true, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, {\n        \"ToastImpl.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"ToastImpl.useComposedRefs[composedRefs]\"]);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)({\n        \"ToastImpl.useCallbackRef[handleClose]\": ()=>{\n            const isFocusInToast = node?.contains(document.activeElement);\n            if (isFocusInToast) context.viewport?.focus();\n            onClose();\n        }\n    }[\"ToastImpl.useCallbackRef[handleClose]\"]);\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ToastImpl.useCallback[startTimer]\": (duration2)=>{\n            if (!duration2 || duration2 === Infinity) return;\n            window.clearTimeout(closeTimerRef.current);\n            closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n            closeTimerRef.current = window.setTimeout(handleClose, duration2);\n        }\n    }[\"ToastImpl.useCallback[startTimer]\"], [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            const viewport = context.viewport;\n            if (viewport) {\n                const handleResume = {\n                    \"ToastImpl.useEffect.handleResume\": ()=>{\n                        startTimer(closeTimerRemainingTimeRef.current);\n                        onResume?.();\n                    }\n                }[\"ToastImpl.useEffect.handleResume\"];\n                const handlePause = {\n                    \"ToastImpl.useEffect.handlePause\": ()=>{\n                        const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                        closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                        window.clearTimeout(closeTimerRef.current);\n                        onPause?.();\n                    }\n                }[\"ToastImpl.useEffect.handlePause\"];\n                viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n                return ({\n                    \"ToastImpl.useEffect\": ()=>{\n                        viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                        viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n                    }\n                })[\"ToastImpl.useEffect\"];\n            }\n        }\n    }[\"ToastImpl.useEffect\"], [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            if (open && !context.isClosePausedRef.current) startTimer(duration);\n        }\n    }[\"ToastImpl.useEffect\"], [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            onToastAdd();\n            return ({\n                \"ToastImpl.useEffect\": ()=>onToastRemove()\n            })[\"ToastImpl.useEffect\"];\n        }\n    }[\"ToastImpl.useEffect\"], [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ToastImpl.useMemo[announceTextContent]\": ()=>{\n            return node ? getAnnounceTextContent(node) : null;\n        }\n    }[\"ToastImpl.useMemo[announceTextContent]\"], [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame({\n        \"ToastAnnounce.useNextFrame\": ()=>setRenderAnnounceText(true)\n    }[\"ToastAnnounce.useNextFrame\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastAnnounce.useEffect\": ()=>{\n            const timer = window.setTimeout({\n                \"ToastAnnounce.useEffect.timer\": ()=>setIsAnnounced(true)\n            }[\"ToastAnnounce.useEffect.timer\"], 1e3);\n            return ({\n                \"ToastAnnounce.useEffect\": ()=>window.clearTimeout(timer)\n            })[\"ToastAnnounce.useEffect\"];\n        }\n    }[\"ToastAnnounce.useEffect\"], []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)({\n        \"useNextFrame.useLayoutEffect\": ()=>{\n            let raf1 = 0;\n            let raf2 = 0;\n            raf1 = window.requestAnimationFrame({\n                \"useNextFrame.useLayoutEffect\": ()=>raf2 = window.requestAnimationFrame(fn)\n            }[\"useNextFrame.useLayoutEffect\"]);\n            return ({\n                \"useNextFrame.useLayoutEffect\": ()=>{\n                    window.cancelAnimationFrame(raf1);\n                    window.cancelAnimationFrame(raf2);\n                }\n            })[\"useNextFrame.useLayoutEffect\"];\n        }\n    }[\"useNextFrame.useLayoutEffect\"], [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXRvYXN0QDEuMi42XzRjMWVkZGRmY2JhMzkwNTU4ZTZmN2RkMTgyMTRlOGZkL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdG9hc3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNHO0FBQ1c7QUFDTDtBQUNDO0FBQ0U7QUFDRDtBQUNYO0FBQ0U7QUFDOEI7QUFDeEI7QUFDTTtBQUNMO0FBQ0Q7QUErRXpCO0FBdkVOLElBQU0sZ0JBQWdCO0FBRXRCLElBQU0sQ0FBQyxZQUFZLGVBQWUscUJBQXFCLElBQUksNEVBQWdCLENBQWUsT0FBTztBQWtCakcsSUFBTSxDQUFDLG9CQUFvQixnQkFBZ0IsSUFBSSwyRUFBa0IsQ0FBQyxTQUFTO0lBQUMscUJBQXFCO0NBQUM7QUFDbEcsSUFBTSxDQUFDLHVCQUF1Qix1QkFBdUIsSUFDbkQsbUJBQThDLGFBQWE7QUEyQjdELElBQU0sZ0JBQThDLENBQUM7SUFDbkQsTUFBTSxFQUNKLGNBQ0EsUUFBUSxnQkFDUixXQUFXLEtBQ1gsaUJBQWlCLFNBQ2pCLGlCQUFpQixJQUNqQixVQUNGLEdBQUk7SUFDSixNQUFNLENBQUMsVUFBVSxXQUFXLElBQVUsNENBQXNDLElBQUk7SUFDaEYsTUFBTSxDQUFDLFlBQVksYUFBYSxJQUFVLDRDQUFTLENBQUM7SUFDcEQsTUFBTSxpQ0FBdUMsMENBQU8sS0FBSztJQUN6RCxNQUFNLG1CQUF5QiwwQ0FBTyxLQUFLO0lBRTNDLElBQUksQ0FBQyxNQUFNLEtBQUssR0FBRztRQUNqQixRQUFRLE1BQ04sd0NBQXdDLGFBQWE7SUFFekQ7SUFFQSxPQUNFLHVFQUFDLFdBQVcsVUFBWDtRQUFvQixPQUFPO1FBQzFCLGlGQUFDO1lBQ0MsT0FBTztZQUNQO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBLGtCQUFrQjtZQUNsQixZQUFrQjs2Q0FBWSxJQUFNO3FEQUFjLENBQUMsWUFBYyxZQUFZLENBQUM7OzRDQUFHLENBQUMsQ0FBQztZQUNuRixlQUFxQjs2Q0FBWSxJQUFNO3FEQUFjLENBQUMsWUFBYyxZQUFZLENBQUM7OzRDQUFHLENBQUMsQ0FBQztZQUN0RjtZQUNBO1lBRUM7UUFBQTtJQUNILENBQ0Y7QUFFSjtBQUVBLGNBQWMsY0FBYztBQU01QixJQUFNLGdCQUFnQjtBQUN0QixJQUFNLDBCQUEwQjtJQUFDLElBQUk7Q0FBQTtBQUNyQyxJQUFNLGlCQUFpQjtBQUN2QixJQUFNLGtCQUFrQjtBQWtCeEIsSUFBTSw4QkFBc0IsOENBQzFCLENBQUMsT0FBd0M7SUFDdkMsTUFBTSxFQUNKLGNBQ0EsU0FBUyx5QkFDVCxRQUFRLDRCQUNSLEdBQUcsZUFDTCxHQUFJO0lBQ0osTUFBTSxVQUFVLHdCQUF3QixlQUFlLFlBQVk7SUFDbkUsTUFBTSxXQUFXLGNBQWMsWUFBWTtJQUMzQyxNQUFNLGFBQW1CLDBDQUF1QixJQUFJO0lBQ3BELE1BQU0sb0JBQTBCLDBDQUEwQixJQUFJO0lBQzlELE1BQU0sb0JBQTBCLDBDQUEwQixJQUFJO0lBQzlELE1BQU0sTUFBWSwwQ0FBNkIsSUFBSTtJQUNuRCxNQUFNLGVBQWUsNkVBQWUsQ0FBQyxjQUFjLEtBQUssUUFBUSxnQkFBZ0I7SUFDaEYsTUFBTSxjQUFjLE9BQU8sS0FBSyxHQUFHLEVBQUUsUUFBUSxRQUFRLEVBQUUsRUFBRSxRQUFRLFVBQVUsRUFBRTtJQUM3RSxNQUFNLFlBQVksUUFBUSxhQUFhO0lBRWpDO21DQUFVO1lBQ2QsTUFBTTt5REFBZ0IsQ0FBQztvQkFHckIsTUFBTSxrQkFDSixPQUFPLFdBQVcsS0FBSyxPQUFPO2lFQUFNLENBQUMsTUFBUyxNQUFjLEdBQUcsS0FBSyxNQUFNLFNBQVMsR0FBRzs7b0JBQ3hGLElBQUksZ0JBQWlCLEtBQUksU0FBUyxNQUFNO2dCQUMxQzs7WUFDQSxTQUFTLGlCQUFpQixXQUFXLGFBQWE7WUFDbEQ7MkNBQU8sSUFBTSxTQUFTLG9CQUFvQixXQUFXLGFBQWE7O1FBQ3BFO2tDQUFHO1FBQUMsTUFBTTtLQUFDO0lBRUw7bUNBQVU7WUFDZCxNQUFNLFVBQVUsV0FBVztZQUMzQixNQUFNLFdBQVcsSUFBSTtZQUNyQixJQUFJLGFBQWEsV0FBVyxVQUFVO2dCQUNwQyxNQUFNOzJEQUFjO3dCQUNsQixJQUFJLENBQUMsUUFBUSxpQkFBaUIsU0FBUzs0QkFDckMsTUFBTSxhQUFhLElBQUksWUFBWSxjQUFjOzRCQUNqRCxTQUFTLGNBQWMsVUFBVTs0QkFDakMsUUFBUSxpQkFBaUIsVUFBVTt3QkFDckM7b0JBQ0Y7O2dCQUVBLE1BQU07NERBQWU7d0JBQ25CLElBQUksUUFBUSxpQkFBaUIsU0FBUzs0QkFDcEMsTUFBTSxjQUFjLElBQUksWUFBWSxlQUFlOzRCQUNuRCxTQUFTLGNBQWMsV0FBVzs0QkFDbEMsUUFBUSxpQkFBaUIsVUFBVTt3QkFDckM7b0JBQ0Y7O2dCQUVBLE1BQU07b0VBQXVCLENBQUM7d0JBQzVCLE1BQU0sdUJBQXVCLENBQUMsUUFBUSxTQUFTLE1BQU0sYUFBNEI7d0JBQ2pGLElBQUkscUJBQXNCLGNBQWE7b0JBQ3pDOztnQkFFQSxNQUFNO3dFQUEyQjt3QkFDL0IsTUFBTSxnQkFBZ0IsUUFBUSxTQUFTLFNBQVMsYUFBYTt3QkFDN0QsSUFBSSxDQUFDLGNBQWUsY0FBYTtvQkFDbkM7O2dCQUdBLFFBQVEsaUJBQWlCLFdBQVcsV0FBVztnQkFDL0MsUUFBUSxpQkFBaUIsWUFBWSxvQkFBb0I7Z0JBQ3pELFFBQVEsaUJBQWlCLGVBQWUsV0FBVztnQkFDbkQsUUFBUSxpQkFBaUIsZ0JBQWdCLHdCQUF3QjtnQkFDakUsT0FBTyxpQkFBaUIsUUFBUSxXQUFXO2dCQUMzQyxPQUFPLGlCQUFpQixTQUFTLFlBQVk7Z0JBQzdDOytDQUFPO3dCQUNMLFFBQVEsb0JBQW9CLFdBQVcsV0FBVzt3QkFDbEQsUUFBUSxvQkFBb0IsWUFBWSxvQkFBb0I7d0JBQzVELFFBQVEsb0JBQW9CLGVBQWUsV0FBVzt3QkFDdEQsUUFBUSxvQkFBb0IsZ0JBQWdCLHdCQUF3Qjt3QkFDcEUsT0FBTyxvQkFBb0IsUUFBUSxXQUFXO3dCQUM5QyxPQUFPLG9CQUFvQixTQUFTLFlBQVk7b0JBQ2xEOztZQUNGO1FBQ0Y7a0NBQUc7UUFBQztRQUFXLFFBQVEsZ0JBQWdCO0tBQUM7SUFFeEMsTUFBTSw4QkFBb0M7a0VBQ3hDLENBQUMsRUFBRSxpQkFBaUI7WUFDbEIsTUFBTSxhQUFhLFNBQVM7WUFDNUIsTUFBTSxxQkFBcUIsV0FBVzs2RkFBSSxDQUFDO29CQUN6QyxNQUFNLFlBQVksVUFBVSxJQUFJO29CQUNoQyxNQUFNLDBCQUEwQjt3QkFBQzsyQkFBYyxzQkFBc0IsU0FBUyxDQUFDO3FCQUFBO29CQUMvRSxPQUFPLHFCQUFxQixhQUN4QiwwQkFDQSx3QkFBd0IsUUFBUTtnQkFDdEMsQ0FBQzs7WUFDRCxRQUNFLHFCQUFxQixhQUFhLG1CQUFtQixRQUFRLElBQUksb0JBQ2pFLEtBQUs7UUFDVDtpRUFDQTtRQUFDLFFBQVE7S0FBQTtJQUdMO21DQUFVO1lBQ2QsTUFBTSxXQUFXLElBQUk7WUFJckIsSUFBSSxVQUFVO2dCQUNaLE1BQU07NkRBQWdCLENBQUM7d0JBQ3JCLE1BQU0sWUFBWSxNQUFNLFVBQVUsTUFBTSxXQUFXLE1BQU07d0JBQ3pELE1BQU0sV0FBVyxNQUFNLFFBQVEsU0FBUyxDQUFDO3dCQUV6QyxJQUFJLFVBQVU7NEJBQ1osTUFBTSxpQkFBaUIsU0FBUzs0QkFDaEMsTUFBTSxxQkFBcUIsTUFBTTs0QkFDakMsTUFBTSxtQkFBbUIsTUFBTSxXQUFXOzRCQUkxQyxJQUFJLG9CQUFvQixvQkFBb0I7Z0NBQzFDLGtCQUFrQixTQUFTLE1BQU07Z0NBQ2pDOzRCQUNGOzRCQUVBLE1BQU0sbUJBQW1CLHFCQUFxQixjQUFjOzRCQUM1RCxNQUFNLG1CQUFtQiw0QkFBNEI7Z0NBQUU7NEJBQWlCLENBQUM7NEJBQ3pFLE1BQU0sUUFBUSxpQkFBaUI7K0VBQVUsQ0FBQyxZQUFjLGNBQWMsY0FBYzs7NEJBQ3BGLElBQUksV0FBVyxpQkFBaUIsTUFBTSxRQUFRLENBQUMsQ0FBQyxHQUFHO2dDQUNqRCxNQUFNLGVBQWU7NEJBQ3ZCLE9BQU87Z0NBSUwscUJBQ0ksa0JBQWtCLFNBQVMsTUFBTSxJQUNqQyxrQkFBa0IsU0FBUyxNQUFNOzRCQUN2Qzt3QkFDRjtvQkFDRjs7Z0JBR0EsU0FBUyxpQkFBaUIsV0FBVyxhQUFhO2dCQUNsRDsrQ0FBTyxJQUFNLFNBQVMsb0JBQW9CLFdBQVcsYUFBYTs7WUFDcEU7UUFDRjtrQ0FBRztRQUFDO1FBQVUsMkJBQTJCO0tBQUM7SUFFMUMsT0FDRSx3RUFBa0IsdUVBQWpCO1FBQ0MsS0FBSztRQUNMLE1BQUs7UUFDTCxjQUFZLE1BQU0sUUFBUSxZQUFZLFdBQVc7UUFFakQsVUFBVTtRQUdWLE9BQU87WUFBRSxlQUFlLFlBQVksU0FBWTtRQUFPO1FBRXREO1lBQUEsYUFDQyx1RUFBQztnQkFDQyxLQUFLO2dCQUNMLDRCQUE0QjtvQkFDMUIsTUFBTSxxQkFBcUIsNEJBQTRCO3dCQUNyRCxrQkFBa0I7b0JBQ3BCLENBQUM7b0JBQ0QsV0FBVyxrQkFBa0I7Z0JBQy9CO1lBQUE7WUFPSix1RUFBQyxXQUFXLE1BQVg7Z0JBQWdCLE9BQU87Z0JBQ3RCLGlGQUFDLGdFQUFTLENBQUMsSUFBVjtvQkFBYSxVQUFVO29CQUFLLEdBQUc7b0JBQWUsS0FBSztnQkFBQSxDQUFjO1lBQUEsQ0FDcEU7WUFDQyxhQUNDLHVFQUFDO2dCQUNDLEtBQUs7Z0JBQ0wsNEJBQTRCO29CQUMxQixNQUFNLHFCQUFxQiw0QkFBNEI7d0JBQ3JELGtCQUFrQjtvQkFDcEIsQ0FBQztvQkFDRCxXQUFXLGtCQUFrQjtnQkFDL0I7WUFBQTtTQUNGO0lBQUE7QUFJUjtBQUdGLGNBQWMsY0FBYztBQUk1QixJQUFNLG1CQUFtQjtBQVF6QixJQUFNLDJCQUFtQiw4Q0FDdkIsQ0FBQyxPQUFPO0lBQ04sTUFBTSxFQUFFLGNBQWMsNEJBQTRCLEdBQUcsV0FBVyxJQUFJO0lBQ3BFLE1BQU0sVUFBVSx3QkFBd0Isa0JBQWtCLFlBQVk7SUFFdEUsT0FDRSx1RUFBQywyRUFBYyxFQUFkO1FBQ0MsZUFBVztRQUNYLFVBQVU7UUFDVCxHQUFHO1FBQ0osS0FBSztRQUVMLE9BQU87WUFBRSxVQUFVO1FBQVE7UUFDM0IsU0FBUyxDQUFDO1lBQ1IsTUFBTSxxQkFBcUIsTUFBTTtZQUNqQyxNQUFNLDZCQUE2QixDQUFDLFFBQVEsVUFBVSxTQUFTLGtCQUFrQjtZQUNqRixJQUFJLDJCQUE0Qiw0QkFBMkI7UUFDN0Q7SUFBQTtBQUdOO0FBR0YsV0FBVyxjQUFjO0FBTXpCLElBQU0sYUFBYTtBQUNuQixJQUFNLG9CQUFvQjtBQUMxQixJQUFNLG1CQUFtQjtBQUN6QixJQUFNLHFCQUFxQjtBQUMzQixJQUFNLGtCQUFrQjtBQWN4QixJQUFNLHNCQUFjLDhDQUNsQixDQUFDLE9BQWdDO0lBQy9CLE1BQU0sRUFBRSxZQUFZLE1BQU0sVUFBVSxhQUFhLGNBQWMsR0FBRyxXQUFXLElBQUk7SUFDakYsTUFBTSxDQUFDLE9BQU8sTUFBTSxPQUFPLElBQUksNEZBQW9CLENBQUM7UUFDbEQsTUFBTTtRQUNOLGFBQWE7UUFDYixVQUFVO0lBQ1osQ0FBQztJQUNELE9BQ0UsdUVBQUMsK0RBQVEsRUFBUjtRQUFTLFNBQVMsY0FBYztRQUMvQixpRkFBQztZQUNDO1lBQ0MsR0FBRztZQUNKLEtBQUs7WUFDTCxTQUFTLElBQU0sUUFBUSxLQUFLO1lBQzVCLFNBQVMsaUZBQWMsQ0FBQyxNQUFNLE9BQU87WUFDckMsVUFBVSxpRkFBYyxDQUFDLE1BQU0sUUFBUTtZQUN2QyxjQUFjLDBFQUFvQixDQUFDLE1BQU0sY0FBYyxDQUFDO2dCQUN0RCxNQUFNLGNBQWMsYUFBYSxjQUFjLE9BQU87WUFDeEQsQ0FBQztZQUNELGFBQWEsMEVBQW9CLENBQUMsTUFBTSxhQUFhLENBQUM7Z0JBQ3BELE1BQU0sRUFBRSxHQUFHLEVBQUUsSUFBSSxNQUFNLE9BQU87Z0JBQzlCLE1BQU0sY0FBYyxhQUFhLGNBQWMsTUFBTTtnQkFDckQsTUFBTSxjQUFjLE1BQU0sWUFBWSw4QkFBOEIsR0FBRyxDQUFDLElBQUk7Z0JBQzVFLE1BQU0sY0FBYyxNQUFNLFlBQVksOEJBQThCLEdBQUcsQ0FBQyxJQUFJO1lBQzlFLENBQUM7WUFDRCxlQUFlLDBFQUFvQixDQUFDLE1BQU0sZUFBZSxDQUFDO2dCQUN4RCxNQUFNLGNBQWMsYUFBYSxjQUFjLFFBQVE7Z0JBQ3ZELE1BQU0sY0FBYyxNQUFNLGVBQWUsNEJBQTRCO2dCQUNyRSxNQUFNLGNBQWMsTUFBTSxlQUFlLDRCQUE0QjtnQkFDckUsTUFBTSxjQUFjLE1BQU0sZUFBZSwyQkFBMkI7Z0JBQ3BFLE1BQU0sY0FBYyxNQUFNLGVBQWUsMkJBQTJCO1lBQ3RFLENBQUM7WUFDRCxZQUFZLDBFQUFvQixDQUFDLE1BQU0sWUFBWSxDQUFDO2dCQUNsRCxNQUFNLEVBQUUsR0FBRyxFQUFFLElBQUksTUFBTSxPQUFPO2dCQUM5QixNQUFNLGNBQWMsYUFBYSxjQUFjLEtBQUs7Z0JBQ3BELE1BQU0sY0FBYyxNQUFNLGVBQWUsNEJBQTRCO2dCQUNyRSxNQUFNLGNBQWMsTUFBTSxlQUFlLDRCQUE0QjtnQkFDckUsTUFBTSxjQUFjLE1BQU0sWUFBWSw2QkFBNkIsR0FBRyxDQUFDLElBQUk7Z0JBQzNFLE1BQU0sY0FBYyxNQUFNLFlBQVksNkJBQTZCLEdBQUcsQ0FBQyxJQUFJO2dCQUMzRSxRQUFRLEtBQUs7WUFDZixDQUFDO1FBQUE7SUFDSCxDQUNGO0FBRUo7QUFHRixNQUFNLGNBQWM7QUFTcEIsSUFBTSxDQUFDLDBCQUEwQiwwQkFBMEIsSUFBSSxtQkFBbUIsWUFBWTtJQUM1RixVQUFVLEVBQUM7QUFDYixDQUFDO0FBc0JELElBQU0sMEJBQWtCLDhDQUN0QixDQUFDLE9BQW9DO0lBQ25DLE1BQU0sRUFDSixjQUNBLE9BQU8sY0FDUCxVQUFVLGNBQ1YsTUFDQSxTQUNBLGlCQUNBLFNBQ0EsVUFDQSxjQUNBLGFBQ0EsZUFDQSxZQUNBLEdBQUcsWUFDTCxHQUFJO0lBQ0osTUFBTSxVQUFVLHdCQUF3QixZQUFZLFlBQVk7SUFDaEUsTUFBTSxDQUFDLE1BQU0sT0FBTyxJQUFVLDRDQUFrQyxJQUFJO0lBQ3BFLE1BQU0sZUFBZSw2RUFBZSxDQUFDO21EQUFjLENBQUNBLFFBQVMsUUFBUUEsS0FBSSxDQUFDOztJQUMxRSxNQUFNLGtCQUF3QiwwQ0FBd0MsSUFBSTtJQUMxRSxNQUFNLGdCQUFzQiwwQ0FBd0MsSUFBSTtJQUN4RSxNQUFNLFdBQVcsZ0JBQWdCLFFBQVE7SUFDekMsTUFBTSx5QkFBK0IsMENBQU8sQ0FBQztJQUM3QyxNQUFNLDZCQUFtQywwQ0FBTyxRQUFRO0lBQ3hELE1BQU0sZ0JBQXNCLDBDQUFPLENBQUM7SUFDcEMsTUFBTSxFQUFFLFlBQVksY0FBYyxJQUFJO0lBQ3RDLE1BQU0sY0FBYyxpRkFBYztpREFBQztZQUdqQyxNQUFNLGlCQUFpQixNQUFNLFNBQVMsU0FBUyxhQUFhO1lBQzVELElBQUksZUFBZ0IsU0FBUSxVQUFVLE1BQU07WUFDNUMsUUFBUTtRQUNWLENBQUM7O0lBRUQsTUFBTSxhQUFtQjs2Q0FDdkIsQ0FBQ0M7WUFDQyxJQUFJLENBQUNBLGFBQVlBLGNBQWEsU0FBVTtZQUN4QyxPQUFPLGFBQWEsY0FBYyxPQUFPO1lBQ3pDLHVCQUF1Qix1QkFBVSxPQUFJLEtBQUssRUFBRSxRQUFRO1lBQ3BELGNBQWMsVUFBVSxPQUFPLFdBQVcsYUFBYUEsU0FBUTtRQUNqRTs0Q0FDQTtRQUFDLFdBQVc7S0FBQTtJQUdSOytCQUFVO1lBQ2QsTUFBTSxXQUFXLFFBQVE7WUFDekIsSUFBSSxVQUFVO2dCQUNaLE1BQU07d0RBQWU7d0JBQ25CLFdBQVcsMkJBQTJCLE9BQU87d0JBQzdDLFdBQVc7b0JBQ2I7O2dCQUNBLE1BQU07dURBQWM7d0JBQ2xCLE1BQU0sMkJBQWMsT0FBSSxLQUFLLEVBQUUsUUFBUSxJQUFJLHVCQUF1Qjt3QkFDbEUsMkJBQTJCLFVBQVUsMkJBQTJCLFVBQVU7d0JBQzFFLE9BQU8sYUFBYSxjQUFjLE9BQU87d0JBQ3pDLFVBQVU7b0JBQ1o7O2dCQUNBLFNBQVMsaUJBQWlCLGdCQUFnQixXQUFXO2dCQUNyRCxTQUFTLGlCQUFpQixpQkFBaUIsWUFBWTtnQkFDdkQ7MkNBQU87d0JBQ0wsU0FBUyxvQkFBb0IsZ0JBQWdCLFdBQVc7d0JBQ3hELFNBQVMsb0JBQW9CLGlCQUFpQixZQUFZO29CQUM1RDs7WUFDRjtRQUNGOzhCQUFHO1FBQUMsUUFBUTtRQUFVO1FBQVU7UUFBUztRQUFVLFVBQVU7S0FBQztJQUt4RDsrQkFBVTtZQUNkLElBQUksUUFBUSxDQUFDLFFBQVEsaUJBQWlCLFFBQVMsWUFBVyxRQUFRO1FBQ3BFOzhCQUFHO1FBQUM7UUFBTTtRQUFVLFFBQVE7UUFBa0IsVUFBVTtLQUFDO0lBRW5EOytCQUFVO1lBQ2QsV0FBVztZQUNYO3VDQUFPLElBQU0sY0FBYzs7UUFDN0I7OEJBQUc7UUFBQztRQUFZLGFBQWE7S0FBQztJQUU5QixNQUFNLHNCQUE0QjtrREFBUTtZQUN4QyxPQUFPLE9BQU8sdUJBQXVCLElBQUksSUFBSTtRQUMvQztpREFBRztRQUFDLElBQUk7S0FBQztJQUVULElBQUksQ0FBQyxRQUFRLFNBQVUsUUFBTztJQUU5QixPQUNFO1FBQ0c7WUFBQSx1QkFDQyx1RUFBQztnQkFDQztnQkFFQSxNQUFLO2dCQUNMLGFBQVcsU0FBUyxlQUFlLGNBQWM7Z0JBQ2pELGVBQVc7Z0JBRVY7WUFBQTtZQUlMLHVFQUFDO2dCQUF5QixPQUFPO2dCQUFjLFNBQVM7Z0JBQ3JELHdCQUFTLG9EQUNSLHVFQUFDLFdBQVcsVUFBWDtvQkFBb0IsT0FBTztvQkFDMUIsaUZBQWtCLHFFQUFqQjt3QkFDQyxTQUFPO3dCQUNQLGlCQUFpQiwwRUFBb0IsQ0FBQyxpQkFBaUI7NEJBQ3JELElBQUksQ0FBQyxRQUFRLCtCQUErQixRQUFTLGFBQVk7NEJBQ2pFLFFBQVEsK0JBQStCLFVBQVU7d0JBQ25ELENBQUM7d0JBRUQsaUZBQUMsZ0VBQVMsQ0FBQyxJQUFWOzRCQUVDLE1BQUs7NEJBQ0wsYUFBVTs0QkFDVixlQUFXOzRCQUNYLFVBQVU7NEJBQ1YsY0FBWSxPQUFPLFNBQVM7NEJBQzVCLHdCQUFzQixRQUFROzRCQUM3QixHQUFHOzRCQUNKLEtBQUs7NEJBQ0wsT0FBTztnQ0FBRSxZQUFZO2dDQUFRLGFBQWE7Z0NBQVEsR0FBRyxNQUFNOzRCQUFNOzRCQUNqRSxXQUFXLDBFQUFvQixDQUFDLE1BQU0sV0FBVyxDQUFDO2dDQUNoRCxJQUFJLE1BQU0sUUFBUSxTQUFVO2dDQUM1QixrQkFBa0IsTUFBTSxXQUFXO2dDQUNuQyxJQUFJLENBQUMsTUFBTSxZQUFZLGtCQUFrQjtvQ0FDdkMsUUFBUSwrQkFBK0IsVUFBVTtvQ0FDakQsWUFBWTtnQ0FDZDs0QkFDRixDQUFDOzRCQUNELGVBQWUsMEVBQW9CLENBQUMsTUFBTSxlQUFlLENBQUM7Z0NBQ3hELElBQUksTUFBTSxXQUFXLEVBQUc7Z0NBQ3hCLGdCQUFnQixVQUFVO29DQUFFLEdBQUcsTUFBTTtvQ0FBUyxHQUFHLE1BQU07Z0NBQVE7NEJBQ2pFLENBQUM7NEJBQ0QsZUFBZSwwRUFBb0IsQ0FBQyxNQUFNLGVBQWUsQ0FBQztnQ0FDeEQsSUFBSSxDQUFDLGdCQUFnQixRQUFTO2dDQUM5QixNQUFNLElBQUksTUFBTSxVQUFVLGdCQUFnQixRQUFRO2dDQUNsRCxNQUFNLElBQUksTUFBTSxVQUFVLGdCQUFnQixRQUFRO2dDQUNsRCxNQUFNLHNCQUFzQixRQUFRLGNBQWMsT0FBTztnQ0FDekQsTUFBTSxvQkFBb0I7b0NBQUM7b0NBQVEsT0FBTztpQ0FBQSxDQUFFLFNBQVMsUUFBUSxjQUFjO2dDQUMzRSxNQUFNLFFBQVE7b0NBQUM7b0NBQVEsSUFBSTtpQ0FBQSxDQUFFLFNBQVMsUUFBUSxjQUFjLElBQ3hELEtBQUssTUFDTCxLQUFLO2dDQUNULE1BQU0sV0FBVyxvQkFBb0IsTUFBTSxHQUFHLENBQUMsSUFBSTtnQ0FDbkQsTUFBTSxXQUFXLENBQUMsb0JBQW9CLE1BQU0sR0FBRyxDQUFDLElBQUk7Z0NBQ3BELE1BQU0sa0JBQWtCLE1BQU0sZ0JBQWdCLFVBQVUsS0FBSztnQ0FDN0QsTUFBTSxRQUFRO29DQUFFLEdBQUc7b0NBQVUsR0FBRztnQ0FBUztnQ0FDekMsTUFBTSxjQUFjO29DQUFFLGVBQWU7b0NBQU87Z0NBQU07Z0NBQ2xELElBQUkscUJBQXFCO29DQUN2QixjQUFjLFVBQVU7b0NBQ3hCLDZCQUE2QixrQkFBa0IsYUFBYSxhQUFhO3dDQUN2RSxVQUFVO29DQUNaLENBQUM7Z0NBQ0gsV0FBVyxtQkFBbUIsT0FBTyxRQUFRLGdCQUFnQixlQUFlLEdBQUc7b0NBQzdFLGNBQWMsVUFBVTtvQ0FDeEIsNkJBQTZCLG1CQUFtQixjQUFjLGFBQWE7d0NBQ3pFLFVBQVU7b0NBQ1osQ0FBQztvQ0FDQSxNQUFNLE9BQXVCLGtCQUFrQixNQUFNLFNBQVM7Z0NBQ2pFLFdBQVcsS0FBSyxJQUFJLENBQUMsSUFBSSxtQkFBbUIsS0FBSyxJQUFJLENBQUMsSUFBSSxpQkFBaUI7b0NBR3pFLGdCQUFnQixVQUFVO2dDQUM1Qjs0QkFDRixDQUFDOzRCQUNELGFBQWEsMEVBQW9CLENBQUMsTUFBTSxhQUFhLENBQUM7Z0NBQ3BELE1BQU0sUUFBUSxjQUFjO2dDQUM1QixNQUFNLFNBQVMsTUFBTTtnQ0FDckIsSUFBSSxPQUFPLGtCQUFrQixNQUFNLFNBQVMsR0FBRztvQ0FDN0MsT0FBTyxzQkFBc0IsTUFBTSxTQUFTO2dDQUM5QztnQ0FDQSxjQUFjLFVBQVU7Z0NBQ3hCLGdCQUFnQixVQUFVO2dDQUMxQixJQUFJLE9BQU87b0NBQ1QsTUFBTSxRQUFRLE1BQU07b0NBQ3BCLE1BQU0sY0FBYzt3Q0FBRSxlQUFlO3dDQUFPO29DQUFNO29DQUNsRCxJQUNFLG1CQUFtQixPQUFPLFFBQVEsZ0JBQWdCLFFBQVEsY0FBYyxHQUN4RTt3Q0FDQSw2QkFBNkIsaUJBQWlCLFlBQVksYUFBYTs0Q0FDckUsVUFBVTt3Q0FDWixDQUFDO29DQUNILE9BQU87d0NBQ0wsNkJBQ0Usb0JBQ0EsZUFDQSxhQUNBOzRDQUNFLFVBQVU7d0NBQ1o7b0NBRUo7b0NBR0EsTUFBTSxpQkFBaUIsU0FBUyxDQUFDQyxTQUFVQSxPQUFNLGVBQWUsR0FBRzt3Q0FDakUsTUFBTTtvQ0FDUixDQUFDO2dDQUNIOzRCQUNGLENBQUM7d0JBQUE7b0JBQ0g7Z0JBQ0YsQ0FDRixHQUNBLFFBQVE7WUFDVixDQUNGO1NBQUE7SUFBQSxDQUNGO0FBRUo7QUFTRixJQUFNLGdCQUE4QyxDQUFDO0lBQ25ELE1BQU0sRUFBRSxjQUFjLFVBQVUsR0FBRyxjQUFjLElBQUk7SUFDckQsTUFBTSxVQUFVLHdCQUF3QixZQUFZLFlBQVk7SUFDaEUsTUFBTSxDQUFDLG9CQUFvQixxQkFBcUIsSUFBVSw0Q0FBUyxLQUFLO0lBQ3hFLE1BQU0sQ0FBQyxhQUFhLGNBQWMsSUFBVSw0Q0FBUyxLQUFLO0lBRzFEO3NDQUFhLElBQU0sc0JBQXNCLElBQUksQ0FBQzs7SUFHeEM7bUNBQVU7WUFDZCxNQUFNLFFBQVEsT0FBTztpREFBVyxJQUFNLGVBQWUsSUFBSTtnREFBRyxHQUFJO1lBQ2hFOzJDQUFPLElBQU0sT0FBTyxhQUFhLEtBQUs7O1FBQ3hDO2tDQUFHLENBQUMsQ0FBQztJQUVMLE9BQU8sY0FBYyxPQUNuQix1RUFBQywyREFBTSxFQUFOO1FBQU8sU0FBTztRQUNiLGlGQUFDLDJFQUFjLEVBQWQ7WUFBZ0IsR0FBRztZQUNqQixnQ0FDQztnQkFDRztvQkFBQSxRQUFRO29CQUFNO29CQUFFO2lCQUFBO1lBQUEsQ0FDbkI7UUFBQSxDQUVKO0lBQUEsQ0FDRjtBQUVKO0FBTUEsSUFBTSxhQUFhO0FBTW5CLElBQU0sMkJBQW1CLDhDQUN2QixDQUFDLE9BQXFDO0lBQ3BDLE1BQU0sRUFBRSxjQUFjLEdBQUcsV0FBVyxJQUFJO0lBQ3hDLE9BQU8sdUVBQUMsZ0VBQVMsQ0FBQyxLQUFWO1FBQWUsR0FBRztRQUFZLEtBQUs7SUFBQSxDQUFjO0FBQzNEO0FBR0YsV0FBVyxjQUFjO0FBTXpCLElBQU0sbUJBQW1CO0FBS3pCLElBQU0saUNBQXlCLDhDQUM3QixDQUFDLE9BQTJDO0lBQzFDLE1BQU0sRUFBRSxjQUFjLEdBQUcsaUJBQWlCLElBQUk7SUFDOUMsT0FBTyx1RUFBQyxnRUFBUyxDQUFDLEtBQVY7UUFBZSxHQUFHO1FBQWtCLEtBQUs7SUFBQSxDQUFjO0FBQ2pFO0FBR0YsaUJBQWlCLGNBQWM7QUFNL0IsSUFBTSxjQUFjO0FBYXBCLElBQU0sNEJBQW9CLDhDQUN4QixDQUFDLE9BQXNDO0lBQ3JDLE1BQU0sRUFBRSxTQUFTLEdBQUcsWUFBWSxJQUFJO0lBRXBDLElBQUksQ0FBQyxRQUFRLEtBQUssR0FBRztRQUNuQixRQUFRLE1BQ04sMENBQTBDLFdBQVc7UUFFdkQsT0FBTztJQUNUO0lBRUEsT0FDRSx1RUFBQztRQUFxQjtRQUFrQixTQUFPO1FBQzdDLGlGQUFDO1lBQVksR0FBRztZQUFhLEtBQUs7UUFBQSxDQUFjO0lBQUEsQ0FDbEQ7QUFFSjtBQUdGLFlBQVksY0FBYztBQU0xQixJQUFNLGFBQWE7QUFNbkIsSUFBTSwyQkFBbUIsOENBQ3ZCLENBQUMsT0FBcUM7SUFDcEMsTUFBTSxFQUFFLGNBQWMsR0FBRyxXQUFXLElBQUk7SUFDeEMsTUFBTSxxQkFBcUIsMkJBQTJCLFlBQVksWUFBWTtJQUU5RSxPQUNFLHVFQUFDO1FBQXFCLFNBQU87UUFDM0IsaUZBQUMsZ0VBQVMsQ0FBQyxRQUFWO1lBQ0MsTUFBSztZQUNKLEdBQUc7WUFDSixLQUFLO1lBQ0wsU0FBUywwRUFBb0IsQ0FBQyxNQUFNLFNBQVMsbUJBQW1CLE9BQU87UUFBQTtJQUN6RSxDQUNGO0FBRUo7QUFHRixXQUFXLGNBQWM7QUFTekIsSUFBTSxxQ0FBNkIsOENBR2pDLENBQUMsT0FBK0M7SUFDaEQsTUFBTSxFQUFFLGNBQWMsU0FBUyxHQUFHLHFCQUFxQixJQUFJO0lBRTNELE9BQ0UsdUVBQUMsZ0VBQVMsQ0FBQyxLQUFWO1FBQ0MscUNBQWtDO1FBQ2xDLGlDQUErQixXQUFXO1FBQ3pDLEdBQUc7UUFDSixLQUFLO0lBQUE7QUFHWCxDQUFDO0FBRUQsU0FBUyx1QkFBdUIsV0FBd0I7SUFDdEQsTUFBTSxjQUF3QixDQUFDO0lBQy9CLE1BQU0sYUFBYSxNQUFNLEtBQUssVUFBVSxVQUFVO0lBRWxELFdBQVcsUUFBUSxDQUFDO1FBQ2xCLElBQUksS0FBSyxhQUFhLEtBQUssYUFBYSxLQUFLLFlBQWEsYUFBWSxLQUFLLEtBQUssV0FBVztRQUMzRixJQUFJLGNBQWMsSUFBSSxHQUFHO1lBQ3ZCLE1BQU0sV0FBVyxLQUFLLGNBQWMsS0FBSyxVQUFVLEtBQUssTUFBTSxZQUFZO1lBQzFFLE1BQU0sYUFBYSxLQUFLLFFBQVEsOEJBQThCO1lBRTlELElBQUksQ0FBQyxVQUFVO2dCQUNiLElBQUksWUFBWTtvQkFDZCxNQUFNLFVBQVUsS0FBSyxRQUFRO29CQUM3QixJQUFJLFFBQVMsYUFBWSxLQUFLLE9BQU87Z0JBQ3ZDLE9BQU87b0JBQ0wsWUFBWSxLQUFLLEdBQUcsdUJBQXVCLElBQUksQ0FBQztnQkFDbEQ7WUFDRjtRQUNGO0lBQ0YsQ0FBQztJQUlELE9BQU87QUFDVDtBQUlBLFNBQVMsNkJBSVAsTUFDQSxTQUNBLFFBQ0EsRUFBRSxTQUFTLEdBQ1g7SUFDQSxNQUFNLGdCQUFnQixPQUFPLGNBQWM7SUFDM0MsTUFBTSxRQUFRLElBQUksWUFBWSxNQUFNO1FBQUUsU0FBUztRQUFNLFlBQVk7UUFBTTtJQUFPLENBQUM7SUFDL0UsSUFBSSxRQUFTLGVBQWMsaUJBQWlCLE1BQU0sU0FBMEI7UUFBRSxNQUFNO0lBQUssQ0FBQztJQUUxRixJQUFJLFVBQVU7UUFDWixzRkFBMkIsQ0FBQyxlQUFlLEtBQUs7SUFDbEQsT0FBTztRQUNMLGNBQWMsY0FBYyxLQUFLO0lBQ25DO0FBQ0Y7QUFFQSxJQUFNLHFCQUFxQixDQUN6QixPQUNBLFdBQ0EsWUFBWTtJQUVaLE1BQU0sU0FBUyxLQUFLLElBQUksTUFBTSxDQUFDO0lBQy9CLE1BQU0sU0FBUyxLQUFLLElBQUksTUFBTSxDQUFDO0lBQy9CLE1BQU0sV0FBVyxTQUFTO0lBQzFCLElBQUksY0FBYyxVQUFVLGNBQWMsU0FBUztRQUNqRCxPQUFPLFlBQVksU0FBUztJQUM5QixPQUFPO1FBQ0wsT0FBTyxDQUFDLFlBQVksU0FBUztJQUMvQjtBQUNGO0FBRUEsU0FBUyxhQUFhLFdBQVcsS0FBTyxDQUFELEVBQUk7SUFDekMsTUFBTSxLQUFLLGlGQUFjLENBQUMsUUFBUTtJQUNsQyxtRkFBZTt3Q0FBQztZQUNkLElBQUksT0FBTztZQUNYLElBQUksT0FBTztZQUNYLE9BQU8sT0FBTztnREFBc0IsSUFBTyxPQUFPLE9BQU8sc0JBQXNCLEVBQUUsQ0FBRTs7WUFDbkY7Z0RBQU87b0JBQ0wsT0FBTyxxQkFBcUIsSUFBSTtvQkFDaEMsT0FBTyxxQkFBcUIsSUFBSTtnQkFDbEM7O1FBQ0Y7dUNBQUc7UUFBQyxFQUFFO0tBQUM7QUFDVDtBQUVBLFNBQVMsY0FBYyxNQUFnQztJQUNyRCxPQUFPLEtBQUssYUFBYSxLQUFLO0FBQ2hDO0FBWUEsU0FBUyxzQkFBc0IsV0FBd0I7SUFDckQsTUFBTSxRQUF1QixDQUFDO0lBQzlCLE1BQU0sU0FBUyxTQUFTLGlCQUFpQixXQUFXLFdBQVcsY0FBYztRQUMzRSxZQUFZLENBQUM7WUFDWCxNQUFNLGdCQUFnQixLQUFLLFlBQVksV0FBVyxLQUFLLFNBQVM7WUFDaEUsSUFBSSxLQUFLLFlBQVksS0FBSyxVQUFVLGNBQWUsUUFBTyxXQUFXO1lBSXJFLE9BQU8sS0FBSyxZQUFZLElBQUksV0FBVyxnQkFBZ0IsV0FBVztRQUNwRTtJQUNGLENBQUM7SUFDRCxNQUFPLE9BQU8sU0FBUyxFQUFHLE1BQU0sS0FBSyxPQUFPLFdBQTBCO0lBR3RFLE9BQU87QUFDVDtBQUVBLFNBQVMsV0FBVyxZQUEyQjtJQUM3QyxNQUFNLDJCQUEyQixTQUFTO0lBQzFDLE9BQU8sV0FBVyxLQUFLLENBQUM7UUFFdEIsSUFBSSxjQUFjLHlCQUEwQixRQUFPO1FBQ25ELFVBQVUsTUFBTTtRQUNoQixPQUFPLFNBQVMsa0JBQWtCO0lBQ3BDLENBQUM7QUFDSDtBQUVBLElBQU0sV0FBVztBQUNqQixJQUFNLFdBQVc7QUFDakIsSUFBTUMsUUFBTztBQUNiLElBQU0sUUFBUTtBQUNkLElBQU0sY0FBYztBQUNwQixJQUFNLFNBQVM7QUFDZixJQUFNLFFBQVEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTVNJXFxHVElDU1xcVUVcXHNyY1xcdG9hc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCAqIGFzIFJlYWN0RE9NIGZyb20gJ3JlYWN0LWRvbSc7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gJ0ByYWRpeC11aS9wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ29tcG9zZWRSZWZzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcyc7XG5pbXBvcnQgeyBjcmVhdGVDb2xsZWN0aW9uIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbGxlY3Rpb24nO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuaW1wb3J0ICogYXMgRGlzbWlzc2FibGVMYXllciBmcm9tICdAcmFkaXgtdWkvcmVhY3QtZGlzbWlzc2FibGUtbGF5ZXInO1xuaW1wb3J0IHsgUG9ydGFsIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXBvcnRhbCc7XG5pbXBvcnQgeyBQcmVzZW5jZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmVzZW5jZSc7XG5pbXBvcnQgeyBQcmltaXRpdmUsIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZic7XG5pbXBvcnQgeyB1c2VDb250cm9sbGFibGVTdGF0ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY29udHJvbGxhYmxlLXN0YXRlJztcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdCc7XG5pbXBvcnQgeyBWaXN1YWxseUhpZGRlbiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC12aXN1YWxseS1oaWRkZW4nO1xuXG5pbXBvcnQgdHlwZSB7IFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBUb2FzdFByb3ZpZGVyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFBST1ZJREVSX05BTUUgPSAnVG9hc3RQcm92aWRlcic7XG5cbmNvbnN0IFtDb2xsZWN0aW9uLCB1c2VDb2xsZWN0aW9uLCBjcmVhdGVDb2xsZWN0aW9uU2NvcGVdID0gY3JlYXRlQ29sbGVjdGlvbjxUb2FzdEVsZW1lbnQ+KCdUb2FzdCcpO1xuXG50eXBlIFN3aXBlRGlyZWN0aW9uID0gJ3VwJyB8ICdkb3duJyB8ICdsZWZ0JyB8ICdyaWdodCc7XG50eXBlIFRvYXN0UHJvdmlkZXJDb250ZXh0VmFsdWUgPSB7XG4gIGxhYmVsOiBzdHJpbmc7XG4gIGR1cmF0aW9uOiBudW1iZXI7XG4gIHN3aXBlRGlyZWN0aW9uOiBTd2lwZURpcmVjdGlvbjtcbiAgc3dpcGVUaHJlc2hvbGQ6IG51bWJlcjtcbiAgdG9hc3RDb3VudDogbnVtYmVyO1xuICB2aWV3cG9ydDogVG9hc3RWaWV3cG9ydEVsZW1lbnQgfCBudWxsO1xuICBvblZpZXdwb3J0Q2hhbmdlKHZpZXdwb3J0OiBUb2FzdFZpZXdwb3J0RWxlbWVudCk6IHZvaWQ7XG4gIG9uVG9hc3RBZGQoKTogdm9pZDtcbiAgb25Ub2FzdFJlbW92ZSgpOiB2b2lkO1xuICBpc0ZvY3VzZWRUb2FzdEVzY2FwZUtleURvd25SZWY6IFJlYWN0Lk11dGFibGVSZWZPYmplY3Q8Ym9vbGVhbj47XG4gIGlzQ2xvc2VQYXVzZWRSZWY6IFJlYWN0Lk11dGFibGVSZWZPYmplY3Q8Ym9vbGVhbj47XG59O1xuXG50eXBlIFNjb3BlZFByb3BzPFA+ID0gUCAmIHsgX19zY29wZVRvYXN0PzogU2NvcGUgfTtcbmNvbnN0IFtjcmVhdGVUb2FzdENvbnRleHQsIGNyZWF0ZVRvYXN0U2NvcGVdID0gY3JlYXRlQ29udGV4dFNjb3BlKCdUb2FzdCcsIFtjcmVhdGVDb2xsZWN0aW9uU2NvcGVdKTtcbmNvbnN0IFtUb2FzdFByb3ZpZGVyUHJvdmlkZXIsIHVzZVRvYXN0UHJvdmlkZXJDb250ZXh0XSA9XG4gIGNyZWF0ZVRvYXN0Q29udGV4dDxUb2FzdFByb3ZpZGVyQ29udGV4dFZhbHVlPihQUk9WSURFUl9OQU1FKTtcblxuaW50ZXJmYWNlIFRvYXN0UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlO1xuICAvKipcbiAgICogQW4gYXV0aG9yLWxvY2FsaXplZCBsYWJlbCBmb3IgZWFjaCB0b2FzdC4gVXNlZCB0byBoZWxwIHNjcmVlbiByZWFkZXIgdXNlcnNcbiAgICogYXNzb2NpYXRlIHRoZSBpbnRlcnJ1cHRpb24gd2l0aCBhIHRvYXN0LlxuICAgKiBAZGVmYXVsdFZhbHVlICdOb3RpZmljYXRpb24nXG4gICAqL1xuICBsYWJlbD86IHN0cmluZztcbiAgLyoqXG4gICAqIFRpbWUgaW4gbWlsbGlzZWNvbmRzIHRoYXQgZWFjaCB0b2FzdCBzaG91bGQgcmVtYWluIHZpc2libGUgZm9yLlxuICAgKiBAZGVmYXVsdFZhbHVlIDUwMDBcbiAgICovXG4gIGR1cmF0aW9uPzogbnVtYmVyO1xuICAvKipcbiAgICogRGlyZWN0aW9uIG9mIHBvaW50ZXIgc3dpcGUgdGhhdCBzaG91bGQgY2xvc2UgdGhlIHRvYXN0LlxuICAgKiBAZGVmYXVsdFZhbHVlICdyaWdodCdcbiAgICovXG4gIHN3aXBlRGlyZWN0aW9uPzogU3dpcGVEaXJlY3Rpb247XG4gIC8qKlxuICAgKiBEaXN0YW5jZSBpbiBwaXhlbHMgdGhhdCB0aGUgc3dpcGUgbXVzdCBwYXNzIGJlZm9yZSBhIGNsb3NlIGlzIHRyaWdnZXJlZC5cbiAgICogQGRlZmF1bHRWYWx1ZSA1MFxuICAgKi9cbiAgc3dpcGVUaHJlc2hvbGQ/OiBudW1iZXI7XG59XG5cbmNvbnN0IFRvYXN0UHJvdmlkZXI6IFJlYWN0LkZDPFRvYXN0UHJvdmlkZXJQcm9wcz4gPSAocHJvcHM6IFNjb3BlZFByb3BzPFRvYXN0UHJvdmlkZXJQcm9wcz4pID0+IHtcbiAgY29uc3Qge1xuICAgIF9fc2NvcGVUb2FzdCxcbiAgICBsYWJlbCA9ICdOb3RpZmljYXRpb24nLFxuICAgIGR1cmF0aW9uID0gNTAwMCxcbiAgICBzd2lwZURpcmVjdGlvbiA9ICdyaWdodCcsXG4gICAgc3dpcGVUaHJlc2hvbGQgPSA1MCxcbiAgICBjaGlsZHJlbixcbiAgfSA9IHByb3BzO1xuICBjb25zdCBbdmlld3BvcnQsIHNldFZpZXdwb3J0XSA9IFJlYWN0LnVzZVN0YXRlPFRvYXN0Vmlld3BvcnRFbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFt0b2FzdENvdW50LCBzZXRUb2FzdENvdW50XSA9IFJlYWN0LnVzZVN0YXRlKDApO1xuICBjb25zdCBpc0ZvY3VzZWRUb2FzdEVzY2FwZUtleURvd25SZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICBjb25zdCBpc0Nsb3NlUGF1c2VkUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcblxuICBpZiAoIWxhYmVsLnRyaW0oKSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICBgSW52YWxpZCBwcm9wIFxcYGxhYmVsXFxgIHN1cHBsaWVkIHRvIFxcYCR7UFJPVklERVJfTkFNRX1cXGAuIEV4cGVjdGVkIG5vbi1lbXB0eSBcXGBzdHJpbmdcXGAuYFxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxDb2xsZWN0aW9uLlByb3ZpZGVyIHNjb3BlPXtfX3Njb3BlVG9hc3R9PlxuICAgICAgPFRvYXN0UHJvdmlkZXJQcm92aWRlclxuICAgICAgICBzY29wZT17X19zY29wZVRvYXN0fVxuICAgICAgICBsYWJlbD17bGFiZWx9XG4gICAgICAgIGR1cmF0aW9uPXtkdXJhdGlvbn1cbiAgICAgICAgc3dpcGVEaXJlY3Rpb249e3N3aXBlRGlyZWN0aW9ufVxuICAgICAgICBzd2lwZVRocmVzaG9sZD17c3dpcGVUaHJlc2hvbGR9XG4gICAgICAgIHRvYXN0Q291bnQ9e3RvYXN0Q291bnR9XG4gICAgICAgIHZpZXdwb3J0PXt2aWV3cG9ydH1cbiAgICAgICAgb25WaWV3cG9ydENoYW5nZT17c2V0Vmlld3BvcnR9XG4gICAgICAgIG9uVG9hc3RBZGQ9e1JlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHNldFRvYXN0Q291bnQoKHByZXZDb3VudCkgPT4gcHJldkNvdW50ICsgMSksIFtdKX1cbiAgICAgICAgb25Ub2FzdFJlbW92ZT17UmVhY3QudXNlQ2FsbGJhY2soKCkgPT4gc2V0VG9hc3RDb3VudCgocHJldkNvdW50KSA9PiBwcmV2Q291bnQgLSAxKSwgW10pfVxuICAgICAgICBpc0ZvY3VzZWRUb2FzdEVzY2FwZUtleURvd25SZWY9e2lzRm9jdXNlZFRvYXN0RXNjYXBlS2V5RG93blJlZn1cbiAgICAgICAgaXNDbG9zZVBhdXNlZFJlZj17aXNDbG9zZVBhdXNlZFJlZn1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Ub2FzdFByb3ZpZGVyUHJvdmlkZXI+XG4gICAgPC9Db2xsZWN0aW9uLlByb3ZpZGVyPlxuICApO1xufTtcblxuVG9hc3RQcm92aWRlci5kaXNwbGF5TmFtZSA9IFBST1ZJREVSX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFRvYXN0Vmlld3BvcnRcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgVklFV1BPUlRfTkFNRSA9ICdUb2FzdFZpZXdwb3J0JztcbmNvbnN0IFZJRVdQT1JUX0RFRkFVTFRfSE9US0VZID0gWydGOCddO1xuY29uc3QgVklFV1BPUlRfUEFVU0UgPSAndG9hc3Qudmlld3BvcnRQYXVzZSc7XG5jb25zdCBWSUVXUE9SVF9SRVNVTUUgPSAndG9hc3Qudmlld3BvcnRSZXN1bWUnO1xuXG50eXBlIFRvYXN0Vmlld3BvcnRFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLm9sPjtcbnR5cGUgUHJpbWl0aXZlT3JkZXJlZExpc3RQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLm9sPjtcbmludGVyZmFjZSBUb2FzdFZpZXdwb3J0UHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVPcmRlcmVkTGlzdFByb3BzIHtcbiAgLyoqXG4gICAqIFRoZSBrZXlzIHRvIHVzZSBhcyB0aGUga2V5Ym9hcmQgc2hvcnRjdXQgdGhhdCB3aWxsIG1vdmUgZm9jdXMgdG8gdGhlIHRvYXN0IHZpZXdwb3J0LlxuICAgKiBAZGVmYXVsdFZhbHVlIFsnRjgnXVxuICAgKi9cbiAgaG90a2V5Pzogc3RyaW5nW107XG4gIC8qKlxuICAgKiBBbiBhdXRob3ItbG9jYWxpemVkIGxhYmVsIGZvciB0aGUgdG9hc3Qgdmlld3BvcnQgdG8gcHJvdmlkZSBjb250ZXh0IGZvciBzY3JlZW4gcmVhZGVyIHVzZXJzXG4gICAqIHdoZW4gbmF2aWdhdGluZyBwYWdlIGxhbmRtYXJrcy4gVGhlIGF2YWlsYWJsZSBge2hvdGtleX1gIHBsYWNlaG9sZGVyIHdpbGwgYmUgcmVwbGFjZWQgZm9yIHlvdS5cbiAgICogQGRlZmF1bHRWYWx1ZSAnTm90aWZpY2F0aW9ucyAoe2hvdGtleX0pJ1xuICAgKi9cbiAgbGFiZWw/OiBzdHJpbmc7XG59XG5cbmNvbnN0IFRvYXN0Vmlld3BvcnQgPSBSZWFjdC5mb3J3YXJkUmVmPFRvYXN0Vmlld3BvcnRFbGVtZW50LCBUb2FzdFZpZXdwb3J0UHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFRvYXN0Vmlld3BvcnRQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIF9fc2NvcGVUb2FzdCxcbiAgICAgIGhvdGtleSA9IFZJRVdQT1JUX0RFRkFVTFRfSE9US0VZLFxuICAgICAgbGFiZWwgPSAnTm90aWZpY2F0aW9ucyAoe2hvdGtleX0pJyxcbiAgICAgIC4uLnZpZXdwb3J0UHJvcHNcbiAgICB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVRvYXN0UHJvdmlkZXJDb250ZXh0KFZJRVdQT1JUX05BTUUsIF9fc2NvcGVUb2FzdCk7XG4gICAgY29uc3QgZ2V0SXRlbXMgPSB1c2VDb2xsZWN0aW9uKF9fc2NvcGVUb2FzdCk7XG4gICAgY29uc3Qgd3JhcHBlclJlZiA9IFJlYWN0LnVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gICAgY29uc3QgaGVhZEZvY3VzUHJveHlSZWYgPSBSZWFjdC51c2VSZWY8Rm9jdXNQcm94eUVsZW1lbnQ+KG51bGwpO1xuICAgIGNvbnN0IHRhaWxGb2N1c1Byb3h5UmVmID0gUmVhY3QudXNlUmVmPEZvY3VzUHJveHlFbGVtZW50PihudWxsKTtcbiAgICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWY8VG9hc3RWaWV3cG9ydEVsZW1lbnQ+KG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZiwgY29udGV4dC5vblZpZXdwb3J0Q2hhbmdlKTtcbiAgICBjb25zdCBob3RrZXlMYWJlbCA9IGhvdGtleS5qb2luKCcrJykucmVwbGFjZSgvS2V5L2csICcnKS5yZXBsYWNlKC9EaWdpdC9nLCAnJyk7XG4gICAgY29uc3QgaGFzVG9hc3RzID0gY29udGV4dC50b2FzdENvdW50ID4gMDtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICAgIC8vIHdlIHVzZSBgZXZlbnQuY29kZWAgYXMgaXQgaXMgY29uc2lzdGVudCByZWdhcmRsZXNzIG9mIG1ldGEga2V5cyB0aGF0IHdlcmUgcHJlc3NlZC5cbiAgICAgICAgLy8gZm9yIGV4YW1wbGUsIGBldmVudC5rZXlgIGZvciBgQ29udHJvbCtBbHQrdGAgaXMgYOKAoGAgYW5kIGB0ICE9PSDigKBgXG4gICAgICAgIGNvbnN0IGlzSG90a2V5UHJlc3NlZCA9XG4gICAgICAgICAgaG90a2V5Lmxlbmd0aCAhPT0gMCAmJiBob3RrZXkuZXZlcnkoKGtleSkgPT4gKGV2ZW50IGFzIGFueSlba2V5XSB8fCBldmVudC5jb2RlID09PSBrZXkpO1xuICAgICAgICBpZiAoaXNIb3RrZXlQcmVzc2VkKSByZWYuY3VycmVudD8uZm9jdXMoKTtcbiAgICAgIH07XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bik7XG4gICAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICAgIH0sIFtob3RrZXldKTtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBjb25zdCB3cmFwcGVyID0gd3JhcHBlclJlZi5jdXJyZW50O1xuICAgICAgY29uc3Qgdmlld3BvcnQgPSByZWYuY3VycmVudDtcbiAgICAgIGlmIChoYXNUb2FzdHMgJiYgd3JhcHBlciAmJiB2aWV3cG9ydCkge1xuICAgICAgICBjb25zdCBoYW5kbGVQYXVzZSA9ICgpID0+IHtcbiAgICAgICAgICBpZiAoIWNvbnRleHQuaXNDbG9zZVBhdXNlZFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICBjb25zdCBwYXVzZUV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KFZJRVdQT1JUX1BBVVNFKTtcbiAgICAgICAgICAgIHZpZXdwb3J0LmRpc3BhdGNoRXZlbnQocGF1c2VFdmVudCk7XG4gICAgICAgICAgICBjb250ZXh0LmlzQ2xvc2VQYXVzZWRSZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IGhhbmRsZVJlc3VtZSA9ICgpID0+IHtcbiAgICAgICAgICBpZiAoY29udGV4dC5pc0Nsb3NlUGF1c2VkUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VtZUV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KFZJRVdQT1JUX1JFU1VNRSk7XG4gICAgICAgICAgICB2aWV3cG9ydC5kaXNwYXRjaEV2ZW50KHJlc3VtZUV2ZW50KTtcbiAgICAgICAgICAgIGNvbnRleHQuaXNDbG9zZVBhdXNlZFJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IGhhbmRsZUZvY3VzT3V0UmVzdW1lID0gKGV2ZW50OiBGb2N1c0V2ZW50KSA9PiB7XG4gICAgICAgICAgY29uc3QgaXNGb2N1c01vdmluZ091dHNpZGUgPSAhd3JhcHBlci5jb250YWlucyhldmVudC5yZWxhdGVkVGFyZ2V0IGFzIEhUTUxFbGVtZW50KTtcbiAgICAgICAgICBpZiAoaXNGb2N1c01vdmluZ091dHNpZGUpIGhhbmRsZVJlc3VtZSgpO1xuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IGhhbmRsZVBvaW50ZXJMZWF2ZVJlc3VtZSA9ICgpID0+IHtcbiAgICAgICAgICBjb25zdCBpc0ZvY3VzSW5zaWRlID0gd3JhcHBlci5jb250YWlucyhkb2N1bWVudC5hY3RpdmVFbGVtZW50KTtcbiAgICAgICAgICBpZiAoIWlzRm9jdXNJbnNpZGUpIGhhbmRsZVJlc3VtZSgpO1xuICAgICAgICB9O1xuXG4gICAgICAgIC8vIFRvYXN0cyBhcmUgbm90IGluIHRoZSB2aWV3cG9ydCBSZWFjdCB0cmVlIHNvIHdlIG5lZWQgdG8gYmluZCBET00gZXZlbnRzXG4gICAgICAgIHdyYXBwZXIuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgd3JhcHBlci5hZGRFdmVudExpc3RlbmVyKCdmb2N1c291dCcsIGhhbmRsZUZvY3VzT3V0UmVzdW1lKTtcbiAgICAgICAgd3JhcHBlci5hZGRFdmVudExpc3RlbmVyKCdwb2ludGVybW92ZScsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgd3JhcHBlci5hZGRFdmVudExpc3RlbmVyKCdwb2ludGVybGVhdmUnLCBoYW5kbGVQb2ludGVyTGVhdmVSZXN1bWUpO1xuICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignYmx1cicsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgaGFuZGxlUmVzdW1lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICB3cmFwcGVyLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3VzaW4nLCBoYW5kbGVQYXVzZSk7XG4gICAgICAgICAgd3JhcHBlci5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c291dCcsIGhhbmRsZUZvY3VzT3V0UmVzdW1lKTtcbiAgICAgICAgICB3cmFwcGVyLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJtb3ZlJywgaGFuZGxlUGF1c2UpO1xuICAgICAgICAgIHdyYXBwZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcigncG9pbnRlcmxlYXZlJywgaGFuZGxlUG9pbnRlckxlYXZlUmVzdW1lKTtcbiAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmx1cicsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignZm9jdXMnLCBoYW5kbGVSZXN1bWUpO1xuICAgICAgICB9O1xuICAgICAgfVxuICAgIH0sIFtoYXNUb2FzdHMsIGNvbnRleHQuaXNDbG9zZVBhdXNlZFJlZl0pO1xuXG4gICAgY29uc3QgZ2V0U29ydGVkVGFiYmFibGVDYW5kaWRhdGVzID0gUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgICAoeyB0YWJiaW5nRGlyZWN0aW9uIH06IHsgdGFiYmluZ0RpcmVjdGlvbjogJ2ZvcndhcmRzJyB8ICdiYWNrd2FyZHMnIH0pID0+IHtcbiAgICAgICAgY29uc3QgdG9hc3RJdGVtcyA9IGdldEl0ZW1zKCk7XG4gICAgICAgIGNvbnN0IHRhYmJhYmxlQ2FuZGlkYXRlcyA9IHRvYXN0SXRlbXMubWFwKCh0b2FzdEl0ZW0pID0+IHtcbiAgICAgICAgICBjb25zdCB0b2FzdE5vZGUgPSB0b2FzdEl0ZW0ucmVmLmN1cnJlbnQhO1xuICAgICAgICAgIGNvbnN0IHRvYXN0VGFiYmFibGVDYW5kaWRhdGVzID0gW3RvYXN0Tm9kZSwgLi4uZ2V0VGFiYmFibGVDYW5kaWRhdGVzKHRvYXN0Tm9kZSldO1xuICAgICAgICAgIHJldHVybiB0YWJiaW5nRGlyZWN0aW9uID09PSAnZm9yd2FyZHMnXG4gICAgICAgICAgICA/IHRvYXN0VGFiYmFibGVDYW5kaWRhdGVzXG4gICAgICAgICAgICA6IHRvYXN0VGFiYmFibGVDYW5kaWRhdGVzLnJldmVyc2UoKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgdGFiYmluZ0RpcmVjdGlvbiA9PT0gJ2ZvcndhcmRzJyA/IHRhYmJhYmxlQ2FuZGlkYXRlcy5yZXZlcnNlKCkgOiB0YWJiYWJsZUNhbmRpZGF0ZXNcbiAgICAgICAgKS5mbGF0KCk7XG4gICAgICB9LFxuICAgICAgW2dldEl0ZW1zXVxuICAgICk7XG5cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3Qgdmlld3BvcnQgPSByZWYuY3VycmVudDtcbiAgICAgIC8vIFdlIHByb2dyYW1tYXRpY2FsbHkgbWFuYWdlIHRhYmJpbmcgYXMgd2UgYXJlIHVuYWJsZSB0byBpbmZsdWVuY2VcbiAgICAgIC8vIHRoZSBzb3VyY2Ugb3JkZXIgd2l0aCBwb3J0YWxzLCB0aGlzIGFsbG93cyB1cyB0byByZXZlcnNlIHRoZVxuICAgICAgLy8gdGFiIG9yZGVyIHNvIHRoYXQgaXQgcnVucyBmcm9tIG1vc3QgcmVjZW50IHRvYXN0IHRvIGxlYXN0XG4gICAgICBpZiAodmlld3BvcnQpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudDogS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgICAgICAgIGNvbnN0IGlzTWV0YUtleSA9IGV2ZW50LmFsdEtleSB8fCBldmVudC5jdHJsS2V5IHx8IGV2ZW50Lm1ldGFLZXk7XG4gICAgICAgICAgY29uc3QgaXNUYWJLZXkgPSBldmVudC5rZXkgPT09ICdUYWInICYmICFpc01ldGFLZXk7XG5cbiAgICAgICAgICBpZiAoaXNUYWJLZXkpIHtcbiAgICAgICAgICAgIGNvbnN0IGZvY3VzZWRFbGVtZW50ID0gZG9jdW1lbnQuYWN0aXZlRWxlbWVudDtcbiAgICAgICAgICAgIGNvbnN0IGlzVGFiYmluZ0JhY2t3YXJkcyA9IGV2ZW50LnNoaWZ0S2V5O1xuICAgICAgICAgICAgY29uc3QgdGFyZ2V0SXNWaWV3cG9ydCA9IGV2ZW50LnRhcmdldCA9PT0gdmlld3BvcnQ7XG5cbiAgICAgICAgICAgIC8vIElmIHdlJ3JlIGJhY2sgdGFiYmluZyBhZnRlciBqdW1waW5nIHRvIHRoZSB2aWV3cG9ydCB0aGVuIHdlIHNpbXBseVxuICAgICAgICAgICAgLy8gcHJveHkgZm9jdXMgb3V0IHRvIHRoZSBwcmVjZWRpbmcgZG9jdW1lbnRcbiAgICAgICAgICAgIGlmICh0YXJnZXRJc1ZpZXdwb3J0ICYmIGlzVGFiYmluZ0JhY2t3YXJkcykge1xuICAgICAgICAgICAgICBoZWFkRm9jdXNQcm94eVJlZi5jdXJyZW50Py5mb2N1cygpO1xuICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGNvbnN0IHRhYmJpbmdEaXJlY3Rpb24gPSBpc1RhYmJpbmdCYWNrd2FyZHMgPyAnYmFja3dhcmRzJyA6ICdmb3J3YXJkcyc7XG4gICAgICAgICAgICBjb25zdCBzb3J0ZWRDYW5kaWRhdGVzID0gZ2V0U29ydGVkVGFiYmFibGVDYW5kaWRhdGVzKHsgdGFiYmluZ0RpcmVjdGlvbiB9KTtcbiAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gc29ydGVkQ2FuZGlkYXRlcy5maW5kSW5kZXgoKGNhbmRpZGF0ZSkgPT4gY2FuZGlkYXRlID09PSBmb2N1c2VkRWxlbWVudCk7XG4gICAgICAgICAgICBpZiAoZm9jdXNGaXJzdChzb3J0ZWRDYW5kaWRhdGVzLnNsaWNlKGluZGV4ICsgMSkpKSB7XG4gICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAvLyBJZiB3ZSBjYW4ndCBmb2N1cyB0aGF0IG1lYW5zIHdlJ3JlIGF0IHRoZSBlZGdlcyBzbyB3ZVxuICAgICAgICAgICAgICAvLyBwcm94eSB0byB0aGUgY29ycmVzcG9uZGluZyBleGl0IHBvaW50IGFuZCBsZXQgdGhlIGJyb3dzZXIgaGFuZGxlXG4gICAgICAgICAgICAgIC8vIHRhYi9zaGlmdCt0YWIga2V5cHJlc3MgYW5kIGltcGxpY2l0bHkgcGFzcyBmb2N1cyB0byB0aGUgbmV4dCB2YWxpZCBlbGVtZW50IGluIHRoZSBkb2N1bWVudFxuICAgICAgICAgICAgICBpc1RhYmJpbmdCYWNrd2FyZHNcbiAgICAgICAgICAgICAgICA/IGhlYWRGb2N1c1Byb3h5UmVmLmN1cnJlbnQ/LmZvY3VzKClcbiAgICAgICAgICAgICAgICA6IHRhaWxGb2N1c1Byb3h5UmVmLmN1cnJlbnQ/LmZvY3VzKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIC8vIFRvYXN0cyBhcmUgbm90IGluIHRoZSB2aWV3cG9ydCBSZWFjdCB0cmVlIHNvIHdlIG5lZWQgdG8gYmluZCBET00gZXZlbnRzXG4gICAgICAgIHZpZXdwb3J0LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHZpZXdwb3J0LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICAgIH1cbiAgICB9LCBbZ2V0SXRlbXMsIGdldFNvcnRlZFRhYmJhYmxlQ2FuZGlkYXRlc10pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxEaXNtaXNzYWJsZUxheWVyLkJyYW5jaFxuICAgICAgICByZWY9e3dyYXBwZXJSZWZ9XG4gICAgICAgIHJvbGU9XCJyZWdpb25cIlxuICAgICAgICBhcmlhLWxhYmVsPXtsYWJlbC5yZXBsYWNlKCd7aG90a2V5fScsIGhvdGtleUxhYmVsKX1cbiAgICAgICAgLy8gRW5zdXJlIHZpcnR1YWwgY3Vyc29yIGZyb20gbGFuZG1hcmtzIG1lbnVzIHRyaWdnZXJzIGZvY3VzL2JsdXIgZm9yIHBhdXNlL3Jlc3VtZVxuICAgICAgICB0YWJJbmRleD17LTF9XG4gICAgICAgIC8vIGluY2FzZSBsaXN0IGhhcyBzaXplIHdoZW4gZW1wdHkgKGUuZy4gcGFkZGluZyksIHdlIHJlbW92ZSBwb2ludGVyIGV2ZW50cyBzb1xuICAgICAgICAvLyBpdCBkb2Vzbid0IHByZXZlbnQgaW50ZXJhY3Rpb25zIHdpdGggcGFnZSBlbGVtZW50cyB0aGF0IGl0IG92ZXJsYXlzXG4gICAgICAgIHN0eWxlPXt7IHBvaW50ZXJFdmVudHM6IGhhc1RvYXN0cyA/IHVuZGVmaW5lZCA6ICdub25lJyB9fVxuICAgICAgPlxuICAgICAgICB7aGFzVG9hc3RzICYmIChcbiAgICAgICAgICA8Rm9jdXNQcm94eVxuICAgICAgICAgICAgcmVmPXtoZWFkRm9jdXNQcm94eVJlZn1cbiAgICAgICAgICAgIG9uRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0PXsoKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHRhYmJhYmxlQ2FuZGlkYXRlcyA9IGdldFNvcnRlZFRhYmJhYmxlQ2FuZGlkYXRlcyh7XG4gICAgICAgICAgICAgICAgdGFiYmluZ0RpcmVjdGlvbjogJ2ZvcndhcmRzJyxcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIGZvY3VzRmlyc3QodGFiYmFibGVDYW5kaWRhdGVzKTtcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgICAgey8qKlxuICAgICAgICAgKiB0YWJpbmRleCBvbiB0aGUgdGhlIGxpc3Qgc28gdGhhdCBpdCBjYW4gYmUgZm9jdXNlZCB3aGVuIGl0ZW1zIGFyZSByZW1vdmVkLiB3ZSBmb2N1c1xuICAgICAgICAgKiB0aGUgbGlzdCBpbnN0ZWFkIG9mIHRoZSB2aWV3cG9ydCBzbyBpdCBhbm5vdW5jZXMgbnVtYmVyIG9mIGl0ZW1zIHJlbWFpbmluZy5cbiAgICAgICAgICovfVxuICAgICAgICA8Q29sbGVjdGlvbi5TbG90IHNjb3BlPXtfX3Njb3BlVG9hc3R9PlxuICAgICAgICAgIDxQcmltaXRpdmUub2wgdGFiSW5kZXg9ey0xfSB7Li4udmlld3BvcnRQcm9wc30gcmVmPXtjb21wb3NlZFJlZnN9IC8+XG4gICAgICAgIDwvQ29sbGVjdGlvbi5TbG90PlxuICAgICAgICB7aGFzVG9hc3RzICYmIChcbiAgICAgICAgICA8Rm9jdXNQcm94eVxuICAgICAgICAgICAgcmVmPXt0YWlsRm9jdXNQcm94eVJlZn1cbiAgICAgICAgICAgIG9uRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0PXsoKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHRhYmJhYmxlQ2FuZGlkYXRlcyA9IGdldFNvcnRlZFRhYmJhYmxlQ2FuZGlkYXRlcyh7XG4gICAgICAgICAgICAgICAgdGFiYmluZ0RpcmVjdGlvbjogJ2JhY2t3YXJkcycsXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBmb2N1c0ZpcnN0KHRhYmJhYmxlQ2FuZGlkYXRlcyk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG4gICAgICA8L0Rpc21pc3NhYmxlTGF5ZXIuQnJhbmNoPlxuICAgICk7XG4gIH1cbik7XG5cblRvYXN0Vmlld3BvcnQuZGlzcGxheU5hbWUgPSBWSUVXUE9SVF9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEZPQ1VTX1BST1hZX05BTUUgPSAnVG9hc3RGb2N1c1Byb3h5JztcblxudHlwZSBGb2N1c1Byb3h5RWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFZpc3VhbGx5SGlkZGVuPjtcbnR5cGUgVmlzdWFsbHlIaWRkZW5Qcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgVmlzdWFsbHlIaWRkZW4+O1xuaW50ZXJmYWNlIEZvY3VzUHJveHlQcm9wcyBleHRlbmRzIFZpc3VhbGx5SGlkZGVuUHJvcHMge1xuICBvbkZvY3VzRnJvbU91dHNpZGVWaWV3cG9ydCgpOiB2b2lkO1xufVxuXG5jb25zdCBGb2N1c1Byb3h5ID0gUmVhY3QuZm9yd2FyZFJlZjxGb2N1c1Byb3h5RWxlbWVudCwgU2NvcGVkUHJvcHM8Rm9jdXNQcm94eVByb3BzPj4oXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlVG9hc3QsIG9uRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0LCAuLi5wcm94eVByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlVG9hc3RQcm92aWRlckNvbnRleHQoRk9DVVNfUFJPWFlfTkFNRSwgX19zY29wZVRvYXN0KTtcblxuICAgIHJldHVybiAoXG4gICAgICA8VmlzdWFsbHlIaWRkZW5cbiAgICAgICAgYXJpYS1oaWRkZW5cbiAgICAgICAgdGFiSW5kZXg9ezB9XG4gICAgICAgIHsuLi5wcm94eVByb3BzfVxuICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgICAgLy8gQXZvaWQgcGFnZSBzY3JvbGxpbmcgd2hlbiBmb2N1cyBpcyBvbiB0aGUgZm9jdXMgcHJveHlcbiAgICAgICAgc3R5bGU9e3sgcG9zaXRpb246ICdmaXhlZCcgfX1cbiAgICAgICAgb25Gb2N1cz17KGV2ZW50KSA9PiB7XG4gICAgICAgICAgY29uc3QgcHJldkZvY3VzZWRFbGVtZW50ID0gZXZlbnQucmVsYXRlZFRhcmdldCBhcyBIVE1MRWxlbWVudCB8IG51bGw7XG4gICAgICAgICAgY29uc3QgaXNGb2N1c0Zyb21PdXRzaWRlVmlld3BvcnQgPSAhY29udGV4dC52aWV3cG9ydD8uY29udGFpbnMocHJldkZvY3VzZWRFbGVtZW50KTtcbiAgICAgICAgICBpZiAoaXNGb2N1c0Zyb21PdXRzaWRlVmlld3BvcnQpIG9uRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0KCk7XG4gICAgICAgIH19XG4gICAgICAvPlxuICAgICk7XG4gIH1cbik7XG5cbkZvY3VzUHJveHkuZGlzcGxheU5hbWUgPSBGT0NVU19QUk9YWV9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBUb2FzdFxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBUT0FTVF9OQU1FID0gJ1RvYXN0JztcbmNvbnN0IFRPQVNUX1NXSVBFX1NUQVJUID0gJ3RvYXN0LnN3aXBlU3RhcnQnO1xuY29uc3QgVE9BU1RfU1dJUEVfTU9WRSA9ICd0b2FzdC5zd2lwZU1vdmUnO1xuY29uc3QgVE9BU1RfU1dJUEVfQ0FOQ0VMID0gJ3RvYXN0LnN3aXBlQ2FuY2VsJztcbmNvbnN0IFRPQVNUX1NXSVBFX0VORCA9ICd0b2FzdC5zd2lwZUVuZCc7XG5cbnR5cGUgVG9hc3RFbGVtZW50ID0gVG9hc3RJbXBsRWxlbWVudDtcbmludGVyZmFjZSBUb2FzdFByb3BzIGV4dGVuZHMgT21pdDxUb2FzdEltcGxQcm9wcywga2V5b2YgVG9hc3RJbXBsUHJpdmF0ZVByb3BzPiB7XG4gIG9wZW4/OiBib29sZWFuO1xuICBkZWZhdWx0T3Blbj86IGJvb2xlYW47XG4gIG9uT3BlbkNoYW5nZT8ob3BlbjogYm9vbGVhbik6IHZvaWQ7XG4gIC8qKlxuICAgKiBVc2VkIHRvIGZvcmNlIG1vdW50aW5nIHdoZW4gbW9yZSBjb250cm9sIGlzIG5lZWRlZC4gVXNlZnVsIHdoZW5cbiAgICogY29udHJvbGxpbmcgYW5pbWF0aW9uIHdpdGggUmVhY3QgYW5pbWF0aW9uIGxpYnJhcmllcy5cbiAgICovXG4gIGZvcmNlTW91bnQ/OiB0cnVlO1xufVxuXG5jb25zdCBUb2FzdCA9IFJlYWN0LmZvcndhcmRSZWY8VG9hc3RFbGVtZW50LCBUb2FzdFByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxUb2FzdFByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBmb3JjZU1vdW50LCBvcGVuOiBvcGVuUHJvcCwgZGVmYXVsdE9wZW4sIG9uT3BlbkNoYW5nZSwgLi4udG9hc3RQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgW29wZW4gPSB0cnVlLCBzZXRPcGVuXSA9IHVzZUNvbnRyb2xsYWJsZVN0YXRlKHtcbiAgICAgIHByb3A6IG9wZW5Qcm9wLFxuICAgICAgZGVmYXVsdFByb3A6IGRlZmF1bHRPcGVuLFxuICAgICAgb25DaGFuZ2U6IG9uT3BlbkNoYW5nZSxcbiAgICB9KTtcbiAgICByZXR1cm4gKFxuICAgICAgPFByZXNlbmNlIHByZXNlbnQ9e2ZvcmNlTW91bnQgfHwgb3Blbn0+XG4gICAgICAgIDxUb2FzdEltcGxcbiAgICAgICAgICBvcGVuPXtvcGVufVxuICAgICAgICAgIHsuLi50b2FzdFByb3BzfVxuICAgICAgICAgIHJlZj17Zm9yd2FyZGVkUmVmfVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldE9wZW4oZmFsc2UpfVxuICAgICAgICAgIG9uUGF1c2U9e3VzZUNhbGxiYWNrUmVmKHByb3BzLm9uUGF1c2UpfVxuICAgICAgICAgIG9uUmVzdW1lPXt1c2VDYWxsYmFja1JlZihwcm9wcy5vblJlc3VtZSl9XG4gICAgICAgICAgb25Td2lwZVN0YXJ0PXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblN3aXBlU3RhcnQsIChldmVudCkgPT4ge1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zZXRBdHRyaWJ1dGUoJ2RhdGEtc3dpcGUnLCAnc3RhcnQnKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgICBvblN3aXBlTW92ZT17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Td2lwZU1vdmUsIChldmVudCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgeyB4LCB5IH0gPSBldmVudC5kZXRhaWwuZGVsdGE7XG4gICAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnNldEF0dHJpYnV0ZSgnZGF0YS1zd2lwZScsICdtb3ZlJyk7XG4gICAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnNldFByb3BlcnR5KCctLXJhZGl4LXRvYXN0LXN3aXBlLW1vdmUteCcsIGAke3h9cHhgKTtcbiAgICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUuc2V0UHJvcGVydHkoJy0tcmFkaXgtdG9hc3Qtc3dpcGUtbW92ZS15JywgYCR7eX1weGApO1xuICAgICAgICAgIH0pfVxuICAgICAgICAgIG9uU3dpcGVDYW5jZWw9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uU3dpcGVDYW5jZWwsIChldmVudCkgPT4ge1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zZXRBdHRyaWJ1dGUoJ2RhdGEtc3dpcGUnLCAnY2FuY2VsJyk7XG4gICAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnJlbW92ZVByb3BlcnR5KCctLXJhZGl4LXRvYXN0LXN3aXBlLW1vdmUteCcpO1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zdHlsZS5yZW1vdmVQcm9wZXJ0eSgnLS1yYWRpeC10b2FzdC1zd2lwZS1tb3ZlLXknKTtcbiAgICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUucmVtb3ZlUHJvcGVydHkoJy0tcmFkaXgtdG9hc3Qtc3dpcGUtZW5kLXgnKTtcbiAgICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUucmVtb3ZlUHJvcGVydHkoJy0tcmFkaXgtdG9hc3Qtc3dpcGUtZW5kLXknKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgICBvblN3aXBlRW5kPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblN3aXBlRW5kLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHsgeCwgeSB9ID0gZXZlbnQuZGV0YWlsLmRlbHRhO1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zZXRBdHRyaWJ1dGUoJ2RhdGEtc3dpcGUnLCAnZW5kJyk7XG4gICAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnJlbW92ZVByb3BlcnR5KCctLXJhZGl4LXRvYXN0LXN3aXBlLW1vdmUteCcpO1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zdHlsZS5yZW1vdmVQcm9wZXJ0eSgnLS1yYWRpeC10b2FzdC1zd2lwZS1tb3ZlLXknKTtcbiAgICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUuc2V0UHJvcGVydHkoJy0tcmFkaXgtdG9hc3Qtc3dpcGUtZW5kLXgnLCBgJHt4fXB4YCk7XG4gICAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnNldFByb3BlcnR5KCctLXJhZGl4LXRvYXN0LXN3aXBlLWVuZC15JywgYCR7eX1weGApO1xuICAgICAgICAgICAgc2V0T3BlbihmYWxzZSk7XG4gICAgICAgICAgfSl9XG4gICAgICAgIC8+XG4gICAgICA8L1ByZXNlbmNlPlxuICAgICk7XG4gIH1cbik7XG5cblRvYXN0LmRpc3BsYXlOYW1lID0gVE9BU1RfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG50eXBlIFN3aXBlRXZlbnQgPSB7IGN1cnJlbnRUYXJnZXQ6IEV2ZW50VGFyZ2V0ICYgVG9hc3RFbGVtZW50IH0gJiBPbWl0PFxuICBDdXN0b21FdmVudDx7IG9yaWdpbmFsRXZlbnQ6IFJlYWN0LlBvaW50ZXJFdmVudDsgZGVsdGE6IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfSB9PixcbiAgJ2N1cnJlbnRUYXJnZXQnXG4+O1xuXG5jb25zdCBbVG9hc3RJbnRlcmFjdGl2ZVByb3ZpZGVyLCB1c2VUb2FzdEludGVyYWN0aXZlQ29udGV4dF0gPSBjcmVhdGVUb2FzdENvbnRleHQoVE9BU1RfTkFNRSwge1xuICBvbkNsb3NlKCkge30sXG59KTtcblxudHlwZSBUb2FzdEltcGxFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLmxpPjtcbnR5cGUgRGlzbWlzc2FibGVMYXllclByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEaXNtaXNzYWJsZUxheWVyLlJvb3Q+O1xudHlwZSBUb2FzdEltcGxQcml2YXRlUHJvcHMgPSB7IG9wZW46IGJvb2xlYW47IG9uQ2xvc2UoKTogdm9pZCB9O1xudHlwZSBQcmltaXRpdmVMaXN0SXRlbVByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBQcmltaXRpdmUubGk+O1xuaW50ZXJmYWNlIFRvYXN0SW1wbFByb3BzIGV4dGVuZHMgVG9hc3RJbXBsUHJpdmF0ZVByb3BzLCBQcmltaXRpdmVMaXN0SXRlbVByb3BzIHtcbiAgdHlwZT86ICdmb3JlZ3JvdW5kJyB8ICdiYWNrZ3JvdW5kJztcbiAgLyoqXG4gICAqIFRpbWUgaW4gbWlsbGlzZWNvbmRzIHRoYXQgdG9hc3Qgc2hvdWxkIHJlbWFpbiB2aXNpYmxlIGZvci4gT3ZlcnJpZGVzIHZhbHVlXG4gICAqIGdpdmVuIHRvIGBUb2FzdFByb3ZpZGVyYC5cbiAgICovXG4gIGR1cmF0aW9uPzogbnVtYmVyO1xuICBvbkVzY2FwZUtleURvd24/OiBEaXNtaXNzYWJsZUxheWVyUHJvcHNbJ29uRXNjYXBlS2V5RG93biddO1xuICBvblBhdXNlPygpOiB2b2lkO1xuICBvblJlc3VtZT8oKTogdm9pZDtcbiAgb25Td2lwZVN0YXJ0PyhldmVudDogU3dpcGVFdmVudCk6IHZvaWQ7XG4gIG9uU3dpcGVNb3ZlPyhldmVudDogU3dpcGVFdmVudCk6IHZvaWQ7XG4gIG9uU3dpcGVDYW5jZWw/KGV2ZW50OiBTd2lwZUV2ZW50KTogdm9pZDtcbiAgb25Td2lwZUVuZD8oZXZlbnQ6IFN3aXBlRXZlbnQpOiB2b2lkO1xufVxuXG5jb25zdCBUb2FzdEltcGwgPSBSZWFjdC5mb3J3YXJkUmVmPFRvYXN0SW1wbEVsZW1lbnQsIFRvYXN0SW1wbFByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxUb2FzdEltcGxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIF9fc2NvcGVUb2FzdCxcbiAgICAgIHR5cGUgPSAnZm9yZWdyb3VuZCcsXG4gICAgICBkdXJhdGlvbjogZHVyYXRpb25Qcm9wLFxuICAgICAgb3BlbixcbiAgICAgIG9uQ2xvc2UsXG4gICAgICBvbkVzY2FwZUtleURvd24sXG4gICAgICBvblBhdXNlLFxuICAgICAgb25SZXN1bWUsXG4gICAgICBvblN3aXBlU3RhcnQsXG4gICAgICBvblN3aXBlTW92ZSxcbiAgICAgIG9uU3dpcGVDYW5jZWwsXG4gICAgICBvblN3aXBlRW5kLFxuICAgICAgLi4udG9hc3RQcm9wc1xuICAgIH0gPSBwcm9wcztcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlVG9hc3RQcm92aWRlckNvbnRleHQoVE9BU1RfTkFNRSwgX19zY29wZVRvYXN0KTtcbiAgICBjb25zdCBbbm9kZSwgc2V0Tm9kZV0gPSBSZWFjdC51c2VTdGF0ZTxUb2FzdEltcGxFbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgKG5vZGUpID0+IHNldE5vZGUobm9kZSkpO1xuICAgIGNvbnN0IHBvaW50ZXJTdGFydFJlZiA9IFJlYWN0LnVzZVJlZjx7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0gfCBudWxsPihudWxsKTtcbiAgICBjb25zdCBzd2lwZURlbHRhUmVmID0gUmVhY3QudXNlUmVmPHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfSB8IG51bGw+KG51bGwpO1xuICAgIGNvbnN0IGR1cmF0aW9uID0gZHVyYXRpb25Qcm9wIHx8IGNvbnRleHQuZHVyYXRpb247XG4gICAgY29uc3QgY2xvc2VUaW1lclN0YXJ0VGltZVJlZiA9IFJlYWN0LnVzZVJlZigwKTtcbiAgICBjb25zdCBjbG9zZVRpbWVyUmVtYWluaW5nVGltZVJlZiA9IFJlYWN0LnVzZVJlZihkdXJhdGlvbik7XG4gICAgY29uc3QgY2xvc2VUaW1lclJlZiA9IFJlYWN0LnVzZVJlZigwKTtcbiAgICBjb25zdCB7IG9uVG9hc3RBZGQsIG9uVG9hc3RSZW1vdmUgfSA9IGNvbnRleHQ7XG4gICAgY29uc3QgaGFuZGxlQ2xvc2UgPSB1c2VDYWxsYmFja1JlZigoKSA9PiB7XG4gICAgICAvLyBmb2N1cyB2aWV3cG9ydCBpZiBmb2N1cyBpcyB3aXRoaW4gdG9hc3QgdG8gcmVhZCB0aGUgcmVtYWluaW5nIHRvYXN0XG4gICAgICAvLyBjb3VudCB0byBTUiB1c2VycyBhbmQgZW5zdXJlIGZvY3VzIGlzbid0IGxvc3RcbiAgICAgIGNvbnN0IGlzRm9jdXNJblRvYXN0ID0gbm9kZT8uY29udGFpbnMoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCk7XG4gICAgICBpZiAoaXNGb2N1c0luVG9hc3QpIGNvbnRleHQudmlld3BvcnQ/LmZvY3VzKCk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfSk7XG5cbiAgICBjb25zdCBzdGFydFRpbWVyID0gUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgICAoZHVyYXRpb246IG51bWJlcikgPT4ge1xuICAgICAgICBpZiAoIWR1cmF0aW9uIHx8IGR1cmF0aW9uID09PSBJbmZpbml0eSkgcmV0dXJuO1xuICAgICAgICB3aW5kb3cuY2xlYXJUaW1lb3V0KGNsb3NlVGltZXJSZWYuY3VycmVudCk7XG4gICAgICAgIGNsb3NlVGltZXJTdGFydFRpbWVSZWYuY3VycmVudCA9IG5ldyBEYXRlKCkuZ2V0VGltZSgpO1xuICAgICAgICBjbG9zZVRpbWVyUmVmLmN1cnJlbnQgPSB3aW5kb3cuc2V0VGltZW91dChoYW5kbGVDbG9zZSwgZHVyYXRpb24pO1xuICAgICAgfSxcbiAgICAgIFtoYW5kbGVDbG9zZV1cbiAgICApO1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGNvbnN0IHZpZXdwb3J0ID0gY29udGV4dC52aWV3cG9ydDtcbiAgICAgIGlmICh2aWV3cG9ydCkge1xuICAgICAgICBjb25zdCBoYW5kbGVSZXN1bWUgPSAoKSA9PiB7XG4gICAgICAgICAgc3RhcnRUaW1lcihjbG9zZVRpbWVyUmVtYWluaW5nVGltZVJlZi5jdXJyZW50KTtcbiAgICAgICAgICBvblJlc3VtZT8uKCk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGhhbmRsZVBhdXNlID0gKCkgPT4ge1xuICAgICAgICAgIGNvbnN0IGVsYXBzZWRUaW1lID0gbmV3IERhdGUoKS5nZXRUaW1lKCkgLSBjbG9zZVRpbWVyU3RhcnRUaW1lUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgY2xvc2VUaW1lclJlbWFpbmluZ1RpbWVSZWYuY3VycmVudCA9IGNsb3NlVGltZXJSZW1haW5pbmdUaW1lUmVmLmN1cnJlbnQgLSBlbGFwc2VkVGltZTtcbiAgICAgICAgICB3aW5kb3cuY2xlYXJUaW1lb3V0KGNsb3NlVGltZXJSZWYuY3VycmVudCk7XG4gICAgICAgICAgb25QYXVzZT8uKCk7XG4gICAgICAgIH07XG4gICAgICAgIHZpZXdwb3J0LmFkZEV2ZW50TGlzdGVuZXIoVklFV1BPUlRfUEFVU0UsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgdmlld3BvcnQuYWRkRXZlbnRMaXN0ZW5lcihWSUVXUE9SVF9SRVNVTUUsIGhhbmRsZVJlc3VtZSk7XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgdmlld3BvcnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihWSUVXUE9SVF9QQVVTRSwgaGFuZGxlUGF1c2UpO1xuICAgICAgICAgIHZpZXdwb3J0LnJlbW92ZUV2ZW50TGlzdGVuZXIoVklFV1BPUlRfUkVTVU1FLCBoYW5kbGVSZXN1bWUpO1xuICAgICAgICB9O1xuICAgICAgfVxuICAgIH0sIFtjb250ZXh0LnZpZXdwb3J0LCBkdXJhdGlvbiwgb25QYXVzZSwgb25SZXN1bWUsIHN0YXJ0VGltZXJdKTtcblxuICAgIC8vIHN0YXJ0IHRpbWVyIHdoZW4gdG9hc3Qgb3BlbnMgb3IgZHVyYXRpb24gY2hhbmdlcy5cbiAgICAvLyB3ZSBpbmNsdWRlIGBvcGVuYCBpbiBkZXBzIGJlY2F1c2UgY2xvc2VkICE9PSB1bm1vdW50ZWQgd2hlbiBhbmltYXRpbmdcbiAgICAvLyBzbyBpdCBjb3VsZCByZW9wZW4gYmVmb3JlIGJlaW5nIGNvbXBsZXRlbHkgdW5tb3VudGVkXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChvcGVuICYmICFjb250ZXh0LmlzQ2xvc2VQYXVzZWRSZWYuY3VycmVudCkgc3RhcnRUaW1lcihkdXJhdGlvbik7XG4gICAgfSwgW29wZW4sIGR1cmF0aW9uLCBjb250ZXh0LmlzQ2xvc2VQYXVzZWRSZWYsIHN0YXJ0VGltZXJdKTtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBvblRvYXN0QWRkKCk7XG4gICAgICByZXR1cm4gKCkgPT4gb25Ub2FzdFJlbW92ZSgpO1xuICAgIH0sIFtvblRvYXN0QWRkLCBvblRvYXN0UmVtb3ZlXSk7XG5cbiAgICBjb25zdCBhbm5vdW5jZVRleHRDb250ZW50ID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgICByZXR1cm4gbm9kZSA/IGdldEFubm91bmNlVGV4dENvbnRlbnQobm9kZSkgOiBudWxsO1xuICAgIH0sIFtub2RlXSk7XG5cbiAgICBpZiAoIWNvbnRleHQudmlld3BvcnQpIHJldHVybiBudWxsO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDw+XG4gICAgICAgIHthbm5vdW5jZVRleHRDb250ZW50ICYmIChcbiAgICAgICAgICA8VG9hc3RBbm5vdW5jZVxuICAgICAgICAgICAgX19zY29wZVRvYXN0PXtfX3Njb3BlVG9hc3R9XG4gICAgICAgICAgICAvLyBUb2FzdHMgYXJlIGFsd2F5cyByb2xlPXN0YXR1cyB0byBhdm9pZCBzdHV0dGVyaW5nIGlzc3VlcyB3aXRoIHJvbGU9YWxlcnQgaW4gU1JzLlxuICAgICAgICAgICAgcm9sZT1cInN0YXR1c1wiXG4gICAgICAgICAgICBhcmlhLWxpdmU9e3R5cGUgPT09ICdmb3JlZ3JvdW5kJyA/ICdhc3NlcnRpdmUnIDogJ3BvbGl0ZSd9XG4gICAgICAgICAgICBhcmlhLWF0b21pY1xuICAgICAgICAgID5cbiAgICAgICAgICAgIHthbm5vdW5jZVRleHRDb250ZW50fVxuICAgICAgICAgIDwvVG9hc3RBbm5vdW5jZT5cbiAgICAgICAgKX1cblxuICAgICAgICA8VG9hc3RJbnRlcmFjdGl2ZVByb3ZpZGVyIHNjb3BlPXtfX3Njb3BlVG9hc3R9IG9uQ2xvc2U9e2hhbmRsZUNsb3NlfT5cbiAgICAgICAgICB7UmVhY3RET00uY3JlYXRlUG9ydGFsKFxuICAgICAgICAgICAgPENvbGxlY3Rpb24uSXRlbVNsb3Qgc2NvcGU9e19fc2NvcGVUb2FzdH0+XG4gICAgICAgICAgICAgIDxEaXNtaXNzYWJsZUxheWVyLlJvb3RcbiAgICAgICAgICAgICAgICBhc0NoaWxkXG4gICAgICAgICAgICAgICAgb25Fc2NhcGVLZXlEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhvbkVzY2FwZUtleURvd24sICgpID0+IHtcbiAgICAgICAgICAgICAgICAgIGlmICghY29udGV4dC5pc0ZvY3VzZWRUb2FzdEVzY2FwZUtleURvd25SZWYuY3VycmVudCkgaGFuZGxlQ2xvc2UoKTtcbiAgICAgICAgICAgICAgICAgIGNvbnRleHQuaXNGb2N1c2VkVG9hc3RFc2NhcGVLZXlEb3duUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQcmltaXRpdmUubGlcbiAgICAgICAgICAgICAgICAgIC8vIEVuc3VyZSB0b2FzdHMgYXJlIGFubm91bmNlZCBhcyBzdGF0dXMgbGlzdCBvciBzdGF0dXMgd2hlbiBmb2N1c2VkXG4gICAgICAgICAgICAgICAgICByb2xlPVwic3RhdHVzXCJcbiAgICAgICAgICAgICAgICAgIGFyaWEtbGl2ZT1cIm9mZlwiXG4gICAgICAgICAgICAgICAgICBhcmlhLWF0b21pY1xuICAgICAgICAgICAgICAgICAgdGFiSW5kZXg9ezB9XG4gICAgICAgICAgICAgICAgICBkYXRhLXN0YXRlPXtvcGVuID8gJ29wZW4nIDogJ2Nsb3NlZCd9XG4gICAgICAgICAgICAgICAgICBkYXRhLXN3aXBlLWRpcmVjdGlvbj17Y29udGV4dC5zd2lwZURpcmVjdGlvbn1cbiAgICAgICAgICAgICAgICAgIHsuLi50b2FzdFByb3BzfVxuICAgICAgICAgICAgICAgICAgcmVmPXtjb21wb3NlZFJlZnN9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB1c2VyU2VsZWN0OiAnbm9uZScsIHRvdWNoQWN0aW9uOiAnbm9uZScsIC4uLnByb3BzLnN0eWxlIH19XG4gICAgICAgICAgICAgICAgICBvbktleURvd249e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uS2V5RG93biwgKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChldmVudC5rZXkgIT09ICdFc2NhcGUnKSByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIG9uRXNjYXBlS2V5RG93bj8uKGV2ZW50Lm5hdGl2ZUV2ZW50KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFldmVudC5uYXRpdmVFdmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5pc0ZvY3VzZWRUb2FzdEVzY2FwZUtleURvd25SZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ2xvc2UoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICBvblBvaW50ZXJEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblBvaW50ZXJEb3duLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50LmJ1dHRvbiAhPT0gMCkgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICBwb2ludGVyU3RhcnRSZWYuY3VycmVudCA9IHsgeDogZXZlbnQuY2xpZW50WCwgeTogZXZlbnQuY2xpZW50WSB9O1xuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICBvblBvaW50ZXJNb3ZlPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblBvaW50ZXJNb3ZlLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFwb2ludGVyU3RhcnRSZWYuY3VycmVudCkgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB4ID0gZXZlbnQuY2xpZW50WCAtIHBvaW50ZXJTdGFydFJlZi5jdXJyZW50Lng7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHkgPSBldmVudC5jbGllbnRZIC0gcG9pbnRlclN0YXJ0UmVmLmN1cnJlbnQueTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFzU3dpcGVNb3ZlU3RhcnRlZCA9IEJvb2xlYW4oc3dpcGVEZWx0YVJlZi5jdXJyZW50KTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNIb3Jpem9udGFsU3dpcGUgPSBbJ2xlZnQnLCAncmlnaHQnXS5pbmNsdWRlcyhjb250ZXh0LnN3aXBlRGlyZWN0aW9uKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY2xhbXAgPSBbJ2xlZnQnLCAndXAnXS5pbmNsdWRlcyhjb250ZXh0LnN3aXBlRGlyZWN0aW9uKVxuICAgICAgICAgICAgICAgICAgICAgID8gTWF0aC5taW5cbiAgICAgICAgICAgICAgICAgICAgICA6IE1hdGgubWF4O1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjbGFtcGVkWCA9IGlzSG9yaXpvbnRhbFN3aXBlID8gY2xhbXAoMCwgeCkgOiAwO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjbGFtcGVkWSA9ICFpc0hvcml6b250YWxTd2lwZSA/IGNsYW1wKDAsIHkpIDogMDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbW92ZVN0YXJ0QnVmZmVyID0gZXZlbnQucG9pbnRlclR5cGUgPT09ICd0b3VjaCcgPyAxMCA6IDI7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlbHRhID0geyB4OiBjbGFtcGVkWCwgeTogY2xhbXBlZFkgfTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXZlbnREZXRhaWwgPSB7IG9yaWdpbmFsRXZlbnQ6IGV2ZW50LCBkZWx0YSB9O1xuICAgICAgICAgICAgICAgICAgICBpZiAoaGFzU3dpcGVNb3ZlU3RhcnRlZCkge1xuICAgICAgICAgICAgICAgICAgICAgIHN3aXBlRGVsdGFSZWYuY3VycmVudCA9IGRlbHRhO1xuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUFuZERpc3BhdGNoQ3VzdG9tRXZlbnQoVE9BU1RfU1dJUEVfTU9WRSwgb25Td2lwZU1vdmUsIGV2ZW50RGV0YWlsLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNjcmV0ZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNEZWx0YUluRGlyZWN0aW9uKGRlbHRhLCBjb250ZXh0LnN3aXBlRGlyZWN0aW9uLCBtb3ZlU3RhcnRCdWZmZXIpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgc3dpcGVEZWx0YVJlZi5jdXJyZW50ID0gZGVsdGE7XG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChUT0FTVF9TV0lQRV9TVEFSVCwgb25Td2lwZVN0YXJ0LCBldmVudERldGFpbCwge1xuICAgICAgICAgICAgICAgICAgICAgICAgZGlzY3JldGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgIChldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQpLnNldFBvaW50ZXJDYXB0dXJlKGV2ZW50LnBvaW50ZXJJZCk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoTWF0aC5hYnMoeCkgPiBtb3ZlU3RhcnRCdWZmZXIgfHwgTWF0aC5hYnMoeSkgPiBtb3ZlU3RhcnRCdWZmZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAvLyBVc2VyIGlzIHN3aXBpbmcgaW4gd3JvbmcgZGlyZWN0aW9uIHNvIHdlIGRpc2FibGUgc3dpcGUgZ2VzdHVyZVxuICAgICAgICAgICAgICAgICAgICAgIC8vIGZvciB0aGUgY3VycmVudCBwb2ludGVyIGRvd24gaW50ZXJhY3Rpb25cbiAgICAgICAgICAgICAgICAgICAgICBwb2ludGVyU3RhcnRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgb25Qb2ludGVyVXA9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uUG9pbnRlclVwLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVsdGEgPSBzd2lwZURlbHRhUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRhcmdldC5oYXNQb2ludGVyQ2FwdHVyZShldmVudC5wb2ludGVySWQpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0LnJlbGVhc2VQb2ludGVyQ2FwdHVyZShldmVudC5wb2ludGVySWQpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHN3aXBlRGVsdGFSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIHBvaW50ZXJTdGFydFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGRlbHRhKSB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgdG9hc3QgPSBldmVudC5jdXJyZW50VGFyZ2V0O1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGV2ZW50RGV0YWlsID0geyBvcmlnaW5hbEV2ZW50OiBldmVudCwgZGVsdGEgfTtcbiAgICAgICAgICAgICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RlbHRhSW5EaXJlY3Rpb24oZGVsdGEsIGNvbnRleHQuc3dpcGVEaXJlY3Rpb24sIGNvbnRleHQuc3dpcGVUaHJlc2hvbGQpXG4gICAgICAgICAgICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50KFRPQVNUX1NXSVBFX0VORCwgb25Td2lwZUVuZCwgZXZlbnREZXRhaWwsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzY3JldGU6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgVE9BU1RfU1dJUEVfQ0FOQ0VMLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBvblN3aXBlQ2FuY2VsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudERldGFpbCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2NyZXRlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAvLyBQcmV2ZW50IGNsaWNrIGV2ZW50IGZyb20gdHJpZ2dlcmluZyBvbiBpdGVtcyB3aXRoaW4gdGhlIHRvYXN0IHdoZW5cbiAgICAgICAgICAgICAgICAgICAgICAvLyBwb2ludGVyIHVwIGlzIHBhcnQgb2YgYSBzd2lwZSBnZXN0dXJlXG4gICAgICAgICAgICAgICAgICAgICAgdG9hc3QuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCAoZXZlbnQpID0+IGV2ZW50LnByZXZlbnREZWZhdWx0KCksIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uY2U6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvRGlzbWlzc2FibGVMYXllci5Sb290PlxuICAgICAgICAgICAgPC9Db2xsZWN0aW9uLkl0ZW1TbG90PixcbiAgICAgICAgICAgIGNvbnRleHQudmlld3BvcnRcbiAgICAgICAgICApfVxuICAgICAgICA8L1RvYXN0SW50ZXJhY3RpdmVQcm92aWRlcj5cbiAgICAgIDwvPlxuICAgICk7XG4gIH1cbik7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuaW50ZXJmYWNlIFRvYXN0QW5ub3VuY2VQcm9wc1xuICBleHRlbmRzIE9taXQ8UmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPCdkaXYnPiwgJ2NoaWxkcmVuJz4sXG4gICAgU2NvcGVkUHJvcHM8eyBjaGlsZHJlbjogc3RyaW5nW10gfT4ge31cblxuY29uc3QgVG9hc3RBbm5vdW5jZTogUmVhY3QuRkM8VG9hc3RBbm5vdW5jZVByb3BzPiA9IChwcm9wczogU2NvcGVkUHJvcHM8VG9hc3RBbm5vdW5jZVByb3BzPikgPT4ge1xuICBjb25zdCB7IF9fc2NvcGVUb2FzdCwgY2hpbGRyZW4sIC4uLmFubm91bmNlUHJvcHMgfSA9IHByb3BzO1xuICBjb25zdCBjb250ZXh0ID0gdXNlVG9hc3RQcm92aWRlckNvbnRleHQoVE9BU1RfTkFNRSwgX19zY29wZVRvYXN0KTtcbiAgY29uc3QgW3JlbmRlckFubm91bmNlVGV4dCwgc2V0UmVuZGVyQW5ub3VuY2VUZXh0XSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQW5ub3VuY2VkLCBzZXRJc0Fubm91bmNlZF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gcmVuZGVyIHRleHQgY29udGVudCBpbiB0aGUgbmV4dCBmcmFtZSB0byBlbnN1cmUgdG9hc3QgaXMgYW5ub3VuY2VkIGluIE5WREFcbiAgdXNlTmV4dEZyYW1lKCgpID0+IHNldFJlbmRlckFubm91bmNlVGV4dCh0cnVlKSk7XG5cbiAgLy8gY2xlYW51cCBhZnRlciBhbm5vdW5jaW5nXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSB3aW5kb3cuc2V0VGltZW91dCgoKSA9PiBzZXRJc0Fubm91bmNlZCh0cnVlKSwgMTAwMCk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5jbGVhclRpbWVvdXQodGltZXIpO1xuICB9LCBbXSk7XG5cbiAgcmV0dXJuIGlzQW5ub3VuY2VkID8gbnVsbCA6IChcbiAgICA8UG9ydGFsIGFzQ2hpbGQ+XG4gICAgICA8VmlzdWFsbHlIaWRkZW4gey4uLmFubm91bmNlUHJvcHN9PlxuICAgICAgICB7cmVuZGVyQW5ub3VuY2VUZXh0ICYmIChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAge2NvbnRleHQubGFiZWx9IHtjaGlsZHJlbn1cbiAgICAgICAgICA8Lz5cbiAgICAgICAgKX1cbiAgICAgIDwvVmlzdWFsbHlIaWRkZW4+XG4gICAgPC9Qb3J0YWw+XG4gICk7XG59O1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBUb2FzdFRpdGxlXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFRJVExFX05BTUUgPSAnVG9hc3RUaXRsZSc7XG5cbnR5cGUgVG9hc3RUaXRsZUVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFRvYXN0VGl0bGVQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHt9XG5cbmNvbnN0IFRvYXN0VGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFRvYXN0VGl0bGVFbGVtZW50LCBUb2FzdFRpdGxlUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFRvYXN0VGl0bGVQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZVRvYXN0LCAuLi50aXRsZVByb3BzIH0gPSBwcm9wcztcbiAgICByZXR1cm4gPFByaW1pdGl2ZS5kaXYgey4uLnRpdGxlUHJvcHN9IHJlZj17Zm9yd2FyZGVkUmVmfSAvPjtcbiAgfVxuKTtcblxuVG9hc3RUaXRsZS5kaXNwbGF5TmFtZSA9IFRJVExFX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFRvYXN0RGVzY3JpcHRpb25cbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgREVTQ1JJUFRJT05fTkFNRSA9ICdUb2FzdERlc2NyaXB0aW9uJztcblxudHlwZSBUb2FzdERlc2NyaXB0aW9uRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFRvYXN0RGVzY3JpcHRpb25Qcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHt9XG5cbmNvbnN0IFRvYXN0RGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFRvYXN0RGVzY3JpcHRpb25FbGVtZW50LCBUb2FzdERlc2NyaXB0aW9uUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFRvYXN0RGVzY3JpcHRpb25Qcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZVRvYXN0LCAuLi5kZXNjcmlwdGlvblByb3BzIH0gPSBwcm9wcztcbiAgICByZXR1cm4gPFByaW1pdGl2ZS5kaXYgey4uLmRlc2NyaXB0aW9uUHJvcHN9IHJlZj17Zm9yd2FyZGVkUmVmfSAvPjtcbiAgfVxuKTtcblxuVG9hc3REZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IERFU0NSSVBUSU9OX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFRvYXN0QWN0aW9uXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEFDVElPTl9OQU1FID0gJ1RvYXN0QWN0aW9uJztcblxudHlwZSBUb2FzdEFjdGlvbkVsZW1lbnQgPSBUb2FzdENsb3NlRWxlbWVudDtcbmludGVyZmFjZSBUb2FzdEFjdGlvblByb3BzIGV4dGVuZHMgVG9hc3RDbG9zZVByb3BzIHtcbiAgLyoqXG4gICAqIEEgc2hvcnQgZGVzY3JpcHRpb24gZm9yIGFuIGFsdGVybmF0ZSB3YXkgdG8gY2Fycnkgb3V0IHRoZSBhY3Rpb24uIEZvciBzY3JlZW4gcmVhZGVyIHVzZXJzXG4gICAqIHdobyB3aWxsIG5vdCBiZSBhYmxlIHRvIG5hdmlnYXRlIHRvIHRoZSBidXR0b24gZWFzaWx5L3F1aWNrbHkuXG4gICAqIEBleGFtcGxlIDxUb2FzdEFjdGlvbiBhbHRUZXh0PVwiR290byBhY2NvdW50IHNldHRpbmdzIHRvIHVwZ3JhZGVcIj5VcGdyYWRlPC9Ub2FzdEFjdGlvbj5cbiAgICogQGV4YW1wbGUgPFRvYXN0QWN0aW9uIGFsdFRleHQ9XCJVbmRvIChBbHQrVSlcIj5VbmRvPC9Ub2FzdEFjdGlvbj5cbiAgICovXG4gIGFsdFRleHQ6IHN0cmluZztcbn1cblxuY29uc3QgVG9hc3RBY3Rpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFRvYXN0QWN0aW9uRWxlbWVudCwgVG9hc3RBY3Rpb25Qcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8VG9hc3RBY3Rpb25Qcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgYWx0VGV4dCwgLi4uYWN0aW9uUHJvcHMgfSA9IHByb3BzO1xuXG4gICAgaWYgKCFhbHRUZXh0LnRyaW0oKSkge1xuICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgYEludmFsaWQgcHJvcCBcXGBhbHRUZXh0XFxgIHN1cHBsaWVkIHRvIFxcYCR7QUNUSU9OX05BTUV9XFxgLiBFeHBlY3RlZCBub24tZW1wdHkgXFxgc3RyaW5nXFxgLmBcbiAgICAgICk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFRvYXN0QW5ub3VuY2VFeGNsdWRlIGFsdFRleHQ9e2FsdFRleHR9IGFzQ2hpbGQ+XG4gICAgICAgIDxUb2FzdENsb3NlIHsuLi5hY3Rpb25Qcm9wc30gcmVmPXtmb3J3YXJkZWRSZWZ9IC8+XG4gICAgICA8L1RvYXN0QW5ub3VuY2VFeGNsdWRlPlxuICAgICk7XG4gIH1cbik7XG5cblRvYXN0QWN0aW9uLmRpc3BsYXlOYW1lID0gQUNUSU9OX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFRvYXN0Q2xvc2VcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQ0xPU0VfTkFNRSA9ICdUb2FzdENsb3NlJztcblxudHlwZSBUb2FzdENsb3NlRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5idXR0b24+O1xudHlwZSBQcmltaXRpdmVCdXR0b25Qcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmJ1dHRvbj47XG5pbnRlcmZhY2UgVG9hc3RDbG9zZVByb3BzIGV4dGVuZHMgUHJpbWl0aXZlQnV0dG9uUHJvcHMge31cblxuY29uc3QgVG9hc3RDbG9zZSA9IFJlYWN0LmZvcndhcmRSZWY8VG9hc3RDbG9zZUVsZW1lbnQsIFRvYXN0Q2xvc2VQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8VG9hc3RDbG9zZVByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlVG9hc3QsIC4uLmNsb3NlUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGludGVyYWN0aXZlQ29udGV4dCA9IHVzZVRvYXN0SW50ZXJhY3RpdmVDb250ZXh0KENMT1NFX05BTUUsIF9fc2NvcGVUb2FzdCk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFRvYXN0QW5ub3VuY2VFeGNsdWRlIGFzQ2hpbGQ+XG4gICAgICAgIDxQcmltaXRpdmUuYnV0dG9uXG4gICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgey4uLmNsb3NlUHJvcHN9XG4gICAgICAgICAgcmVmPXtmb3J3YXJkZWRSZWZ9XG4gICAgICAgICAgb25DbGljaz17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25DbGljaywgaW50ZXJhY3RpdmVDb250ZXh0Lm9uQ2xvc2UpfVxuICAgICAgICAvPlxuICAgICAgPC9Ub2FzdEFubm91bmNlRXhjbHVkZT5cbiAgICApO1xuICB9XG4pO1xuXG5Ub2FzdENsb3NlLmRpc3BsYXlOYW1lID0gQ0xPU0VfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAqL1xuXG50eXBlIFRvYXN0QW5ub3VuY2VFeGNsdWRlRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFRvYXN0QW5ub3VuY2VFeGNsdWRlUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVEaXZQcm9wcyB7XG4gIGFsdFRleHQ/OiBzdHJpbmc7XG59XG5cbmNvbnN0IFRvYXN0QW5ub3VuY2VFeGNsdWRlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgVG9hc3RBbm5vdW5jZUV4Y2x1ZGVFbGVtZW50LFxuICBUb2FzdEFubm91bmNlRXhjbHVkZVByb3BzXG4+KChwcm9wczogU2NvcGVkUHJvcHM8VG9hc3RBbm5vdW5jZUV4Y2x1ZGVQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7IF9fc2NvcGVUb2FzdCwgYWx0VGV4dCwgLi4uYW5ub3VuY2VFeGNsdWRlUHJvcHMgfSA9IHByb3BzO1xuXG4gIHJldHVybiAoXG4gICAgPFByaW1pdGl2ZS5kaXZcbiAgICAgIGRhdGEtcmFkaXgtdG9hc3QtYW5ub3VuY2UtZXhjbHVkZT1cIlwiXG4gICAgICBkYXRhLXJhZGl4LXRvYXN0LWFubm91bmNlLWFsdD17YWx0VGV4dCB8fCB1bmRlZmluZWR9XG4gICAgICB7Li4uYW5ub3VuY2VFeGNsdWRlUHJvcHN9XG4gICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAvPlxuICApO1xufSk7XG5cbmZ1bmN0aW9uIGdldEFubm91bmNlVGV4dENvbnRlbnQoY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb25zdCB0ZXh0Q29udGVudDogc3RyaW5nW10gPSBbXTtcbiAgY29uc3QgY2hpbGROb2RlcyA9IEFycmF5LmZyb20oY29udGFpbmVyLmNoaWxkTm9kZXMpO1xuXG4gIGNoaWxkTm9kZXMuZm9yRWFjaCgobm9kZSkgPT4ge1xuICAgIGlmIChub2RlLm5vZGVUeXBlID09PSBub2RlLlRFWFRfTk9ERSAmJiBub2RlLnRleHRDb250ZW50KSB0ZXh0Q29udGVudC5wdXNoKG5vZGUudGV4dENvbnRlbnQpO1xuICAgIGlmIChpc0hUTUxFbGVtZW50KG5vZGUpKSB7XG4gICAgICBjb25zdCBpc0hpZGRlbiA9IG5vZGUuYXJpYUhpZGRlbiB8fCBub2RlLmhpZGRlbiB8fCBub2RlLnN0eWxlLmRpc3BsYXkgPT09ICdub25lJztcbiAgICAgIGNvbnN0IGlzRXhjbHVkZWQgPSBub2RlLmRhdGFzZXQucmFkaXhUb2FzdEFubm91bmNlRXhjbHVkZSA9PT0gJyc7XG5cbiAgICAgIGlmICghaXNIaWRkZW4pIHtcbiAgICAgICAgaWYgKGlzRXhjbHVkZWQpIHtcbiAgICAgICAgICBjb25zdCBhbHRUZXh0ID0gbm9kZS5kYXRhc2V0LnJhZGl4VG9hc3RBbm5vdW5jZUFsdDtcbiAgICAgICAgICBpZiAoYWx0VGV4dCkgdGV4dENvbnRlbnQucHVzaChhbHRUZXh0KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0ZXh0Q29udGVudC5wdXNoKC4uLmdldEFubm91bmNlVGV4dENvbnRlbnQobm9kZSkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9KTtcblxuICAvLyBXZSByZXR1cm4gYSBjb2xsZWN0aW9uIG9mIHRleHQgcmF0aGVyIHRoYW4gYSBzaW5nbGUgY29uY2F0ZW5hdGVkIHN0cmluZy5cbiAgLy8gVGhpcyBhbGxvd3MgU1IgVk8gdG8gbmF0dXJhbGx5IHBhdXNlIGJyZWFrIGJldHdlZW4gbm9kZXMgd2hpbGUgYW5ub3VuY2luZy5cbiAgcmV0dXJuIHRleHRDb250ZW50O1xufVxuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tICovXG5cbmZ1bmN0aW9uIGhhbmRsZUFuZERpc3BhdGNoQ3VzdG9tRXZlbnQ8XG4gIEUgZXh0ZW5kcyBDdXN0b21FdmVudCxcbiAgUmVhY3RFdmVudCBleHRlbmRzIFJlYWN0LlN5bnRoZXRpY0V2ZW50LFxuPihcbiAgbmFtZTogc3RyaW5nLFxuICBoYW5kbGVyOiAoKGV2ZW50OiBFKSA9PiB2b2lkKSB8IHVuZGVmaW5lZCxcbiAgZGV0YWlsOiB7IG9yaWdpbmFsRXZlbnQ6IFJlYWN0RXZlbnQgfSAmIChFIGV4dGVuZHMgQ3VzdG9tRXZlbnQ8aW5mZXIgRD4gPyBEIDogbmV2ZXIpLFxuICB7IGRpc2NyZXRlIH06IHsgZGlzY3JldGU6IGJvb2xlYW4gfVxuKSB7XG4gIGNvbnN0IGN1cnJlbnRUYXJnZXQgPSBkZXRhaWwub3JpZ2luYWxFdmVudC5jdXJyZW50VGFyZ2V0IGFzIEhUTUxFbGVtZW50O1xuICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudChuYW1lLCB7IGJ1YmJsZXM6IHRydWUsIGNhbmNlbGFibGU6IHRydWUsIGRldGFpbCB9KTtcbiAgaWYgKGhhbmRsZXIpIGN1cnJlbnRUYXJnZXQuYWRkRXZlbnRMaXN0ZW5lcihuYW1lLCBoYW5kbGVyIGFzIEV2ZW50TGlzdGVuZXIsIHsgb25jZTogdHJ1ZSB9KTtcblxuICBpZiAoZGlzY3JldGUpIHtcbiAgICBkaXNwYXRjaERpc2NyZXRlQ3VzdG9tRXZlbnQoY3VycmVudFRhcmdldCwgZXZlbnQpO1xuICB9IGVsc2Uge1xuICAgIGN1cnJlbnRUYXJnZXQuZGlzcGF0Y2hFdmVudChldmVudCk7XG4gIH1cbn1cblxuY29uc3QgaXNEZWx0YUluRGlyZWN0aW9uID0gKFxuICBkZWx0YTogeyB4OiBudW1iZXI7IHk6IG51bWJlciB9LFxuICBkaXJlY3Rpb246IFN3aXBlRGlyZWN0aW9uLFxuICB0aHJlc2hvbGQgPSAwXG4pID0+IHtcbiAgY29uc3QgZGVsdGFYID0gTWF0aC5hYnMoZGVsdGEueCk7XG4gIGNvbnN0IGRlbHRhWSA9IE1hdGguYWJzKGRlbHRhLnkpO1xuICBjb25zdCBpc0RlbHRhWCA9IGRlbHRhWCA+IGRlbHRhWTtcbiAgaWYgKGRpcmVjdGlvbiA9PT0gJ2xlZnQnIHx8IGRpcmVjdGlvbiA9PT0gJ3JpZ2h0Jykge1xuICAgIHJldHVybiBpc0RlbHRhWCAmJiBkZWx0YVggPiB0aHJlc2hvbGQ7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuICFpc0RlbHRhWCAmJiBkZWx0YVkgPiB0aHJlc2hvbGQ7XG4gIH1cbn07XG5cbmZ1bmN0aW9uIHVzZU5leHRGcmFtZShjYWxsYmFjayA9ICgpID0+IHt9KSB7XG4gIGNvbnN0IGZuID0gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGxldCByYWYxID0gMDtcbiAgICBsZXQgcmFmMiA9IDA7XG4gICAgcmFmMSA9IHdpbmRvdy5yZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCkgPT4gKHJhZjIgPSB3aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKGZuKSkpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cuY2FuY2VsQW5pbWF0aW9uRnJhbWUocmFmMSk7XG4gICAgICB3aW5kb3cuY2FuY2VsQW5pbWF0aW9uRnJhbWUocmFmMik7XG4gICAgfTtcbiAgfSwgW2ZuXSk7XG59XG5cbmZ1bmN0aW9uIGlzSFRNTEVsZW1lbnQobm9kZTogYW55KTogbm9kZSBpcyBIVE1MRWxlbWVudCB7XG4gIHJldHVybiBub2RlLm5vZGVUeXBlID09PSBub2RlLkVMRU1FTlRfTk9ERTtcbn1cblxuLyoqXG4gKiBSZXR1cm5zIGEgbGlzdCBvZiBwb3RlbnRpYWwgdGFiYmFibGUgY2FuZGlkYXRlcy5cbiAqXG4gKiBOT1RFOiBUaGlzIGlzIG9ubHkgYSBjbG9zZSBhcHByb3hpbWF0aW9uLiBGb3IgZXhhbXBsZSBpdCBkb2Vzbid0IHRha2UgaW50byBhY2NvdW50IGNhc2VzIGxpa2Ugd2hlblxuICogZWxlbWVudHMgYXJlIG5vdCB2aXNpYmxlLiBUaGlzIGNhbm5vdCBiZSB3b3JrZWQgb3V0IGVhc2lseSBieSBqdXN0IHJlYWRpbmcgYSBwcm9wZXJ0eSwgYnV0IHJhdGhlclxuICogbmVjZXNzaXRhdGUgcnVudGltZSBrbm93bGVkZ2UgKGNvbXB1dGVkIHN0eWxlcywgZXRjKS4gV2UgZGVhbCB3aXRoIHRoZXNlIGNhc2VzIHNlcGFyYXRlbHkuXG4gKlxuICogU2VlOiBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvVHJlZVdhbGtlclxuICogQ3JlZGl0OiBodHRwczovL2dpdGh1Yi5jb20vZGlzY29yZC9mb2N1cy1sYXllcnMvYmxvYi9tYXN0ZXIvc3JjL3V0aWwvd3JhcEZvY3VzLnRzeCNMMVxuICovXG5mdW5jdGlvbiBnZXRUYWJiYWJsZUNhbmRpZGF0ZXMoY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb25zdCBub2RlczogSFRNTEVsZW1lbnRbXSA9IFtdO1xuICBjb25zdCB3YWxrZXIgPSBkb2N1bWVudC5jcmVhdGVUcmVlV2Fsa2VyKGNvbnRhaW5lciwgTm9kZUZpbHRlci5TSE9XX0VMRU1FTlQsIHtcbiAgICBhY2NlcHROb2RlOiAobm9kZTogYW55KSA9PiB7XG4gICAgICBjb25zdCBpc0hpZGRlbklucHV0ID0gbm9kZS50YWdOYW1lID09PSAnSU5QVVQnICYmIG5vZGUudHlwZSA9PT0gJ2hpZGRlbic7XG4gICAgICBpZiAobm9kZS5kaXNhYmxlZCB8fCBub2RlLmhpZGRlbiB8fCBpc0hpZGRlbklucHV0KSByZXR1cm4gTm9kZUZpbHRlci5GSUxURVJfU0tJUDtcbiAgICAgIC8vIGAudGFiSW5kZXhgIGlzIG5vdCB0aGUgc2FtZSBhcyB0aGUgYHRhYmluZGV4YCBhdHRyaWJ1dGUuIEl0IHdvcmtzIG9uIHRoZVxuICAgICAgLy8gcnVudGltZSdzIHVuZGVyc3RhbmRpbmcgb2YgdGFiYmFiaWxpdHksIHNvIHRoaXMgYXV0b21hdGljYWxseSBhY2NvdW50c1xuICAgICAgLy8gZm9yIGFueSBraW5kIG9mIGVsZW1lbnQgdGhhdCBjb3VsZCBiZSB0YWJiZWQgdG8uXG4gICAgICByZXR1cm4gbm9kZS50YWJJbmRleCA+PSAwID8gTm9kZUZpbHRlci5GSUxURVJfQUNDRVBUIDogTm9kZUZpbHRlci5GSUxURVJfU0tJUDtcbiAgICB9LFxuICB9KTtcbiAgd2hpbGUgKHdhbGtlci5uZXh0Tm9kZSgpKSBub2Rlcy5wdXNoKHdhbGtlci5jdXJyZW50Tm9kZSBhcyBIVE1MRWxlbWVudCk7XG4gIC8vIHdlIGRvIG5vdCB0YWtlIGludG8gYWNjb3VudCB0aGUgb3JkZXIgb2Ygbm9kZXMgd2l0aCBwb3NpdGl2ZSBgdGFiSW5kZXhgIGFzIGl0XG4gIC8vIGhpbmRlcnMgYWNjZXNzaWJpbGl0eSB0byBoYXZlIHRhYiBvcmRlciBkaWZmZXJlbnQgZnJvbSB2aXN1YWwgb3JkZXIuXG4gIHJldHVybiBub2Rlcztcbn1cblxuZnVuY3Rpb24gZm9jdXNGaXJzdChjYW5kaWRhdGVzOiBIVE1MRWxlbWVudFtdKSB7XG4gIGNvbnN0IHByZXZpb3VzbHlGb2N1c2VkRWxlbWVudCA9IGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQ7XG4gIHJldHVybiBjYW5kaWRhdGVzLnNvbWUoKGNhbmRpZGF0ZSkgPT4ge1xuICAgIC8vIGlmIGZvY3VzIGlzIGFscmVhZHkgd2hlcmUgd2Ugd2FudCB0byBnbywgd2UgZG9uJ3Qgd2FudCB0byBrZWVwIGdvaW5nIHRocm91Z2ggdGhlIGNhbmRpZGF0ZXNcbiAgICBpZiAoY2FuZGlkYXRlID09PSBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQpIHJldHVybiB0cnVlO1xuICAgIGNhbmRpZGF0ZS5mb2N1cygpO1xuICAgIHJldHVybiBkb2N1bWVudC5hY3RpdmVFbGVtZW50ICE9PSBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQ7XG4gIH0pO1xufVxuXG5jb25zdCBQcm92aWRlciA9IFRvYXN0UHJvdmlkZXI7XG5jb25zdCBWaWV3cG9ydCA9IFRvYXN0Vmlld3BvcnQ7XG5jb25zdCBSb290ID0gVG9hc3Q7XG5jb25zdCBUaXRsZSA9IFRvYXN0VGl0bGU7XG5jb25zdCBEZXNjcmlwdGlvbiA9IFRvYXN0RGVzY3JpcHRpb247XG5jb25zdCBBY3Rpb24gPSBUb2FzdEFjdGlvbjtcbmNvbnN0IENsb3NlID0gVG9hc3RDbG9zZTtcblxuZXhwb3J0IHtcbiAgY3JlYXRlVG9hc3RTY29wZSxcbiAgLy9cbiAgVG9hc3RQcm92aWRlcixcbiAgVG9hc3RWaWV3cG9ydCxcbiAgVG9hc3QsXG4gIFRvYXN0VGl0bGUsXG4gIFRvYXN0RGVzY3JpcHRpb24sXG4gIFRvYXN0QWN0aW9uLFxuICBUb2FzdENsb3NlLFxuICAvL1xuICBQcm92aWRlcixcbiAgVmlld3BvcnQsXG4gIFJvb3QsXG4gIFRpdGxlLFxuICBEZXNjcmlwdGlvbixcbiAgQWN0aW9uLFxuICBDbG9zZSxcbn07XG5leHBvcnQgdHlwZSB7XG4gIFRvYXN0UHJvdmlkZXJQcm9wcyxcbiAgVG9hc3RWaWV3cG9ydFByb3BzLFxuICBUb2FzdFByb3BzLFxuICBUb2FzdFRpdGxlUHJvcHMsXG4gIFRvYXN0RGVzY3JpcHRpb25Qcm9wcyxcbiAgVG9hc3RBY3Rpb25Qcm9wcyxcbiAgVG9hc3RDbG9zZVByb3BzLFxufTtcbiJdLCJuYW1lcyI6WyJub2RlIiwiZHVyYXRpb24iLCJldmVudCIsIlJvb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.6_4c1edddfcba390558e6f7dd18214e8fd/node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ })

};
;