"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-direction@1_b66ac1e1812a295f6340accaf05f5518";
exports.ids = ["vendor-chunks/@radix-ui+react-direction@1_b66ac1e1812a295f6340accaf05f5518"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_b66ac1e1812a295f6340accaf05f5518/node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-direction@1_b66ac1e1812a295f6340accaf05f5518/node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/Direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LWRpcmVjdGlvbkAxX2I2NmFjMWUxODEyYTI5NWY2MzQwYWNjYWYwNWY1NTE4L25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtZGlyZWN0aW9uL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDUztBQUN4Qyx1QkFBdUIsZ0RBQW1CO0FBQzFDO0FBQ0EsVUFBVSxnQkFBZ0I7QUFDMUIseUJBQXlCLHNEQUFHLDhCQUE4QixzQkFBc0I7QUFDaEY7QUFDQTtBQUNBLG9CQUFvQiw2Q0FBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNU0lcXEdUSUNTXFxVRVxcRnJvbnRlbmQtRGVwb3JTTS1sZW9cXG5vZGVfbW9kdWxlc1xcLnBucG1cXEByYWRpeC11aStyZWFjdC1kaXJlY3Rpb25AMV9iNjZhYzFlMTgxMmEyOTVmNjM0MGFjY2FmMDVmNTUxOFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LWRpcmVjdGlvblxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2RpcmVjdGlvbi9zcmMvRGlyZWN0aW9uLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBEaXJlY3Rpb25Db250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh2b2lkIDApO1xudmFyIERpcmVjdGlvblByb3ZpZGVyID0gKHByb3BzKSA9PiB7XG4gIGNvbnN0IHsgZGlyLCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KERpcmVjdGlvbkNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGRpciwgY2hpbGRyZW4gfSk7XG59O1xuZnVuY3Rpb24gdXNlRGlyZWN0aW9uKGxvY2FsRGlyKSB7XG4gIGNvbnN0IGdsb2JhbERpciA9IFJlYWN0LnVzZUNvbnRleHQoRGlyZWN0aW9uQ29udGV4dCk7XG4gIHJldHVybiBsb2NhbERpciB8fCBnbG9iYWxEaXIgfHwgXCJsdHJcIjtcbn1cbnZhciBQcm92aWRlciA9IERpcmVjdGlvblByb3ZpZGVyO1xuZXhwb3J0IHtcbiAgRGlyZWN0aW9uUHJvdmlkZXIsXG4gIFByb3ZpZGVyLFxuICB1c2VEaXJlY3Rpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_b66ac1e1812a295f6340accaf05f5518/node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ })

};
;