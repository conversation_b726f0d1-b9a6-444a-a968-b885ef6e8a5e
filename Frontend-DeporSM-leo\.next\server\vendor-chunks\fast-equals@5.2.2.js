"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-equals@5.2.2";
exports.ids = ["vendor-chunks/fast-equals@5.2.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/fast-equals@5.2.2/node_modules/fast-equals/dist/esm/index.mjs":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/fast-equals@5.2.2/node_modules/fast-equals/dist/esm/index.mjs ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circularDeepEqual: () => (/* binding */ circularDeepEqual),\n/* harmony export */   circularShallowEqual: () => (/* binding */ circularShallowEqual),\n/* harmony export */   createCustomEqual: () => (/* binding */ createCustomEqual),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual),\n/* harmony export */   sameValueZeroEqual: () => (/* binding */ sameValueZeroEqual),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   strictCircularDeepEqual: () => (/* binding */ strictCircularDeepEqual),\n/* harmony export */   strictCircularShallowEqual: () => (/* binding */ strictCircularShallowEqual),\n/* harmony export */   strictDeepEqual: () => (/* binding */ strictDeepEqual),\n/* harmony export */   strictShallowEqual: () => (/* binding */ strictShallowEqual)\n/* harmony export */ });\nvar getOwnPropertyNames = Object.getOwnPropertyNames, getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Combine two comparators into a single comparators.\n */\nfunction combineComparators(comparatorA, comparatorB) {\n    return function isEqual(a, b, state) {\n        return comparatorA(a, b, state) && comparatorB(a, b, state);\n    };\n}\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nfunction createIsCircular(areItemsEqual) {\n    return function isCircular(a, b, state) {\n        if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n            return areItemsEqual(a, b, state);\n        }\n        var cache = state.cache;\n        var cachedA = cache.get(a);\n        var cachedB = cache.get(b);\n        if (cachedA && cachedB) {\n            return cachedA === b && cachedB === a;\n        }\n        cache.set(a, b);\n        cache.set(b, a);\n        var result = areItemsEqual(a, b, state);\n        cache.delete(a);\n        cache.delete(b);\n        return result;\n    };\n}\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nfunction getStrictProperties(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Whether the object contains the property passed as an own property.\n */\nvar hasOwn = Object.hasOwn ||\n    (function (object, property) {\n        return hasOwnProperty.call(object, property);\n    });\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nfunction sameValueZeroEqual(a, b) {\n    return a === b || (!a && !b && a !== a && b !== b);\n}\n\nvar PREACT_VNODE = '__v';\nvar PREACT_OWNER = '__o';\nvar REACT_OWNER = '_owner';\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, keys = Object.keys;\n/**\n * Whether the arrays are equal in value.\n */\nfunction areArraysEqual(a, b, state) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the dates passed are equal in value.\n */\nfunction areDatesEqual(a, b) {\n    return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n/**\n * Whether the errors passed are equal in value.\n */\nfunction areErrorsEqual(a, b) {\n    return (a.name === b.name &&\n        a.message === b.message &&\n        a.cause === b.cause &&\n        a.stack === b.stack);\n}\n/**\n * Whether the functions passed are equal in value.\n */\nfunction areFunctionsEqual(a, b) {\n    return a === b;\n}\n/**\n * Whether the `Map`s are equal in value.\n */\nfunction areMapsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n        return false;\n    }\n    if (!size) {\n        return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.entries();\n    var aResult;\n    var bResult;\n    var index = 0;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.entries();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            if (matchedIndices[matchIndex]) {\n                matchIndex++;\n                continue;\n            }\n            var aEntry = aResult.value;\n            var bEntry = bResult.value;\n            if (state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) &&\n                state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)) {\n                hasMatch = matchedIndices[matchIndex] = true;\n                break;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n        index++;\n    }\n    return true;\n}\n/**\n * Whether the numbers are equal in value.\n */\nvar areNumbersEqual = sameValueZeroEqual;\n/**\n * Whether the objects are equal in value.\n */\nfunction areObjectsEqual(a, b, state) {\n    var properties = keys(a);\n    var index = properties.length;\n    if (keys(b).length !== index) {\n        return false;\n    }\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        if (!isPropertyEqual(a, b, state, properties[index])) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nfunction areObjectsEqualStrict(a, b, state) {\n    var properties = getStrictProperties(a);\n    var index = properties.length;\n    if (getStrictProperties(b).length !== index) {\n        return false;\n    }\n    var property;\n    var descriptorA;\n    var descriptorB;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        property = properties[index];\n        if (!isPropertyEqual(a, b, state, property)) {\n            return false;\n        }\n        descriptorA = getOwnPropertyDescriptor(a, property);\n        descriptorB = getOwnPropertyDescriptor(b, property);\n        if ((descriptorA || descriptorB) &&\n            (!descriptorA ||\n                !descriptorB ||\n                descriptorA.configurable !== descriptorB.configurable ||\n                descriptorA.enumerable !== descriptorB.enumerable ||\n                descriptorA.writable !== descriptorB.writable)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nfunction arePrimitiveWrappersEqual(a, b) {\n    return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n/**\n * Whether the regexps passed are equal in value.\n */\nfunction areRegExpsEqual(a, b) {\n    return a.source === b.source && a.flags === b.flags;\n}\n/**\n * Whether the `Set`s are equal in value.\n */\nfunction areSetsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n        return false;\n    }\n    if (!size) {\n        return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.values();\n    var aResult;\n    var bResult;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.values();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            if (!matchedIndices[matchIndex] &&\n                state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state)) {\n                hasMatch = matchedIndices[matchIndex] = true;\n                break;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the TypedArray instances are equal in value.\n */\nfunction areTypedArraysEqual(a, b) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (a[index] !== b[index]) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the URL instances are equal in value.\n */\nfunction areUrlsEqual(a, b) {\n    return (a.hostname === b.hostname &&\n        a.pathname === b.pathname &&\n        a.protocol === b.protocol &&\n        a.port === b.port &&\n        a.hash === b.hash &&\n        a.username === b.username &&\n        a.password === b.password);\n}\nfunction isPropertyEqual(a, b, state, property) {\n    if ((property === REACT_OWNER ||\n        property === PREACT_OWNER ||\n        property === PREACT_VNODE) &&\n        (a.$$typeof || b.$$typeof)) {\n        return true;\n    }\n    return (hasOwn(b, property) &&\n        state.equals(a[property], b[property], property, property, a, b, state));\n}\n\nvar ARGUMENTS_TAG = '[object Arguments]';\nvar BOOLEAN_TAG = '[object Boolean]';\nvar DATE_TAG = '[object Date]';\nvar ERROR_TAG = '[object Error]';\nvar MAP_TAG = '[object Map]';\nvar NUMBER_TAG = '[object Number]';\nvar OBJECT_TAG = '[object Object]';\nvar REG_EXP_TAG = '[object RegExp]';\nvar SET_TAG = '[object Set]';\nvar STRING_TAG = '[object String]';\nvar URL_TAG = '[object URL]';\nvar isArray = Array.isArray;\nvar isTypedArray = typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nvar assign = Object.assign;\nvar getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nfunction createEqualityComparator(_a) {\n    var areArraysEqual = _a.areArraysEqual, areDatesEqual = _a.areDatesEqual, areErrorsEqual = _a.areErrorsEqual, areFunctionsEqual = _a.areFunctionsEqual, areMapsEqual = _a.areMapsEqual, areNumbersEqual = _a.areNumbersEqual, areObjectsEqual = _a.areObjectsEqual, arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual, areRegExpsEqual = _a.areRegExpsEqual, areSetsEqual = _a.areSetsEqual, areTypedArraysEqual = _a.areTypedArraysEqual, areUrlsEqual = _a.areUrlsEqual;\n    /**\n     * compare the value of the two objects and return true if they are equivalent in values\n     */\n    return function comparator(a, b, state) {\n        // If the items are strictly equal, no need to do a value comparison.\n        if (a === b) {\n            return true;\n        }\n        // If either of the items are nullish and fail the strictly equal check\n        // above, then they must be unequal.\n        if (a == null || b == null) {\n            return false;\n        }\n        var type = typeof a;\n        if (type !== typeof b) {\n            return false;\n        }\n        if (type !== 'object') {\n            if (type === 'number') {\n                return areNumbersEqual(a, b, state);\n            }\n            if (type === 'function') {\n                return areFunctionsEqual(a, b, state);\n            }\n            // If a primitive value that is not strictly equal, it must be unequal.\n            return false;\n        }\n        var constructor = a.constructor;\n        // Checks are listed in order of commonality of use-case:\n        //   1. Common complex object types (plain object, array)\n        //   2. Common data values (date, regexp)\n        //   3. Less-common complex object types (map, set)\n        //   4. Less-common data values (promise, primitive wrappers)\n        // Inherently this is both subjective and assumptive, however\n        // when reviewing comparable libraries in the wild this order\n        // appears to be generally consistent.\n        // Constructors should match, otherwise there is potential for false positives\n        // between class and subclass or custom object and POJO.\n        if (constructor !== b.constructor) {\n            return false;\n        }\n        // `isPlainObject` only checks against the object's own realm. Cross-realm\n        // comparisons are rare, and will be handled in the ultimate fallback, so\n        // we can avoid capturing the string tag.\n        if (constructor === Object) {\n            return areObjectsEqual(a, b, state);\n        }\n        // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n        // the string tag or doing an `instanceof` check.\n        if (isArray(a)) {\n            return areArraysEqual(a, b, state);\n        }\n        // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n        // capturing the string tag or comparing against all possible constructors.\n        if (isTypedArray != null && isTypedArray(a)) {\n            return areTypedArraysEqual(a, b, state);\n        }\n        // Try to fast-path equality checks for other complex object types in the\n        // same realm to avoid capturing the string tag. Strict equality is used\n        // instead of `instanceof` because it is more performant for the common\n        // use-case. If someone is subclassing a native class, it will be handled\n        // with the string tag comparison.\n        if (constructor === Date) {\n            return areDatesEqual(a, b, state);\n        }\n        if (constructor === RegExp) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (constructor === Map) {\n            return areMapsEqual(a, b, state);\n        }\n        if (constructor === Set) {\n            return areSetsEqual(a, b, state);\n        }\n        // Since this is a custom object, capture the string tag to determing its type.\n        // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n        var tag = getTag(a);\n        if (tag === DATE_TAG) {\n            return areDatesEqual(a, b, state);\n        }\n        // For RegExp, the properties are not enumerable, and therefore will give false positives if\n        // tested like a standard object.\n        if (tag === REG_EXP_TAG) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (tag === MAP_TAG) {\n            return areMapsEqual(a, b, state);\n        }\n        if (tag === SET_TAG) {\n            return areSetsEqual(a, b, state);\n        }\n        if (tag === OBJECT_TAG) {\n            // The exception for value comparison is custom `Promise`-like class instances. These should\n            // be treated the same as standard `Promise` objects, which means strict equality, and if\n            // it reaches this point then that strict equality comparison has already failed.\n            return (typeof a.then !== 'function' &&\n                typeof b.then !== 'function' &&\n                areObjectsEqual(a, b, state));\n        }\n        // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n        // enumerable, and therefore will give false positives if tested like a standard object.\n        if (tag === URL_TAG) {\n            return areUrlsEqual(a, b, state);\n        }\n        // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n        // enumerable, and therefore will give false positives if tested like a standard object.\n        if (tag === ERROR_TAG) {\n            return areErrorsEqual(a, b, state);\n        }\n        // If an arguments tag, it should be treated as a standard object.\n        if (tag === ARGUMENTS_TAG) {\n            return areObjectsEqual(a, b, state);\n        }\n        // As the penultimate fallback, check if the values passed are primitive wrappers. This\n        // is very rare in modern JS, which is why it is deprioritized compared to all other object\n        // types.\n        if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n            return arePrimitiveWrappersEqual(a, b, state);\n        }\n        // If not matching any tags that require a specific type of comparison, then we hard-code false because\n        // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n        //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n        //     comparison that can be made.\n        //   - For types that can be introspected, but rarely have requirements to be compared\n        //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n        //     use-cases (may be included in a future release, if requested enough).\n        //   - For types that can be introspected but do not have an objective definition of what\n        //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n        // In all cases, these decisions should be reevaluated based on changes to the language and\n        // common development practices.\n        return false;\n    };\n}\n/**\n * Create the configuration object used for building comparators.\n */\nfunction createEqualityComparatorConfig(_a) {\n    var circular = _a.circular, createCustomConfig = _a.createCustomConfig, strict = _a.strict;\n    var config = {\n        areArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areArraysEqual,\n        areDatesEqual: areDatesEqual,\n        areErrorsEqual: areErrorsEqual,\n        areFunctionsEqual: areFunctionsEqual,\n        areMapsEqual: strict\n            ? combineComparators(areMapsEqual, areObjectsEqualStrict)\n            : areMapsEqual,\n        areNumbersEqual: areNumbersEqual,\n        areObjectsEqual: strict\n            ? areObjectsEqualStrict\n            : areObjectsEqual,\n        arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n        areRegExpsEqual: areRegExpsEqual,\n        areSetsEqual: strict\n            ? combineComparators(areSetsEqual, areObjectsEqualStrict)\n            : areSetsEqual,\n        areTypedArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areTypedArraysEqual,\n        areUrlsEqual: areUrlsEqual,\n    };\n    if (createCustomConfig) {\n        config = assign({}, config, createCustomConfig(config));\n    }\n    if (circular) {\n        var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n        var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n        var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n        var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n        config = assign({}, config, {\n            areArraysEqual: areArraysEqual$1,\n            areMapsEqual: areMapsEqual$1,\n            areObjectsEqual: areObjectsEqual$1,\n            areSetsEqual: areSetsEqual$1,\n        });\n    }\n    return config;\n}\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nfunction createInternalEqualityComparator(compare) {\n    return function (a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n        return compare(a, b, state);\n    };\n}\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nfunction createIsEqual(_a) {\n    var circular = _a.circular, comparator = _a.comparator, createState = _a.createState, equals = _a.equals, strict = _a.strict;\n    if (createState) {\n        return function isEqual(a, b) {\n            var _a = createState(), _b = _a.cache, cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b, meta = _a.meta;\n            return comparator(a, b, {\n                cache: cache,\n                equals: equals,\n                meta: meta,\n                strict: strict,\n            });\n        };\n    }\n    if (circular) {\n        return function isEqual(a, b) {\n            return comparator(a, b, {\n                cache: new WeakMap(),\n                equals: equals,\n                meta: undefined,\n                strict: strict,\n            });\n        };\n    }\n    var state = {\n        cache: undefined,\n        equals: equals,\n        meta: undefined,\n        strict: strict,\n    };\n    return function isEqual(a, b) {\n        return comparator(a, b, state);\n    };\n}\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nvar deepEqual = createCustomEqual();\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nvar strictDeepEqual = createCustomEqual({ strict: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nvar circularDeepEqual = createCustomEqual({ circular: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularDeepEqual = createCustomEqual({\n    circular: true,\n    strict: true,\n});\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nvar shallowEqual = createCustomEqual({\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nvar strictShallowEqual = createCustomEqual({\n    strict: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nvar circularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n    strict: true,\n});\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nfunction createCustomEqual(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.circular, circular = _a === void 0 ? false : _a, createCustomInternalComparator = options.createInternalComparator, createState = options.createState, _b = options.strict, strict = _b === void 0 ? false : _b;\n    var config = createEqualityComparatorConfig(options);\n    var comparator = createEqualityComparator(config);\n    var equals = createCustomInternalComparator\n        ? createCustomInternalComparator(comparator)\n        : createInternalEqualityComparator(comparator);\n    return createIsEqual({ circular: circular, comparator: comparator, createState: createState, equals: equals, strict: strict });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZmFzdC1lcXVhbHNANS4yLjIvbm9kZV9tb2R1bGVzL2Zhc3QtZXF1YWxzL2Rpc3QvZXNtL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGNBQWM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLGdCQUFnQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0Qyw0QkFBNEI7QUFDeEUsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsNEJBQTRCO0FBQ3hFLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLDRCQUE0QjtBQUN4RSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLDRCQUE0QjtBQUN4RTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNHQUFzRztBQUNqSTs7QUFFNk07QUFDN00iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTVNJXFxHVElDU1xcVUVcXEZyb250ZW5kLURlcG9yU00tbGVvXFxub2RlX21vZHVsZXNcXC5wbnBtXFxmYXN0LWVxdWFsc0A1LjIuMlxcbm9kZV9tb2R1bGVzXFxmYXN0LWVxdWFsc1xcZGlzdFxcZXNtXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGdldE93blByb3BlcnR5TmFtZXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcywgZ2V0T3duUHJvcGVydHlTeW1ib2xzID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scztcbnZhciBoYXNPd25Qcm9wZXJ0eSA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHk7XG4vKipcbiAqIENvbWJpbmUgdHdvIGNvbXBhcmF0b3JzIGludG8gYSBzaW5nbGUgY29tcGFyYXRvcnMuXG4gKi9cbmZ1bmN0aW9uIGNvbWJpbmVDb21wYXJhdG9ycyhjb21wYXJhdG9yQSwgY29tcGFyYXRvckIpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gaXNFcXVhbChhLCBiLCBzdGF0ZSkge1xuICAgICAgICByZXR1cm4gY29tcGFyYXRvckEoYSwgYiwgc3RhdGUpICYmIGNvbXBhcmF0b3JCKGEsIGIsIHN0YXRlKTtcbiAgICB9O1xufVxuLyoqXG4gKiBXcmFwIHRoZSBwcm92aWRlZCBgYXJlSXRlbXNFcXVhbGAgbWV0aG9kIHRvIG1hbmFnZSB0aGUgY2lyY3VsYXIgc3RhdGUsIGFsbG93aW5nXG4gKiBmb3IgY2lyY3VsYXIgcmVmZXJlbmNlcyB0byBiZSBzYWZlbHkgaW5jbHVkZWQgaW4gdGhlIGNvbXBhcmlzb24gd2l0aG91dCBjcmVhdGluZ1xuICogc3RhY2sgb3ZlcmZsb3dzLlxuICovXG5mdW5jdGlvbiBjcmVhdGVJc0NpcmN1bGFyKGFyZUl0ZW1zRXF1YWwpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gaXNDaXJjdWxhcihhLCBiLCBzdGF0ZSkge1xuICAgICAgICBpZiAoIWEgfHwgIWIgfHwgdHlwZW9mIGEgIT09ICdvYmplY3QnIHx8IHR5cGVvZiBiICE9PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgcmV0dXJuIGFyZUl0ZW1zRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIHZhciBjYWNoZSA9IHN0YXRlLmNhY2hlO1xuICAgICAgICB2YXIgY2FjaGVkQSA9IGNhY2hlLmdldChhKTtcbiAgICAgICAgdmFyIGNhY2hlZEIgPSBjYWNoZS5nZXQoYik7XG4gICAgICAgIGlmIChjYWNoZWRBICYmIGNhY2hlZEIpIHtcbiAgICAgICAgICAgIHJldHVybiBjYWNoZWRBID09PSBiICYmIGNhY2hlZEIgPT09IGE7XG4gICAgICAgIH1cbiAgICAgICAgY2FjaGUuc2V0KGEsIGIpO1xuICAgICAgICBjYWNoZS5zZXQoYiwgYSk7XG4gICAgICAgIHZhciByZXN1bHQgPSBhcmVJdGVtc0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgY2FjaGUuZGVsZXRlKGEpO1xuICAgICAgICBjYWNoZS5kZWxldGUoYik7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbn1cbi8qKlxuICogR2V0IHRoZSBwcm9wZXJ0aWVzIHRvIHN0cmljdGx5IGV4YW1pbmUsIHdoaWNoIGluY2x1ZGUgYm90aCBvd24gcHJvcGVydGllcyB0aGF0IGFyZVxuICogbm90IGVudW1lcmFibGUgYW5kIHN5bWJvbCBwcm9wZXJ0aWVzLlxuICovXG5mdW5jdGlvbiBnZXRTdHJpY3RQcm9wZXJ0aWVzKG9iamVjdCkge1xuICAgIHJldHVybiBnZXRPd25Qcm9wZXJ0eU5hbWVzKG9iamVjdCkuY29uY2F0KGdldE93blByb3BlcnR5U3ltYm9scyhvYmplY3QpKTtcbn1cbi8qKlxuICogV2hldGhlciB0aGUgb2JqZWN0IGNvbnRhaW5zIHRoZSBwcm9wZXJ0eSBwYXNzZWQgYXMgYW4gb3duIHByb3BlcnR5LlxuICovXG52YXIgaGFzT3duID0gT2JqZWN0Lmhhc093biB8fFxuICAgIChmdW5jdGlvbiAob2JqZWN0LCBwcm9wZXJ0eSkge1xuICAgICAgICByZXR1cm4gaGFzT3duUHJvcGVydHkuY2FsbChvYmplY3QsIHByb3BlcnR5KTtcbiAgICB9KTtcbi8qKlxuICogV2hldGhlciB0aGUgdmFsdWVzIHBhc3NlZCBhcmUgc3RyaWN0bHkgZXF1YWwgb3IgYm90aCBOYU4uXG4gKi9cbmZ1bmN0aW9uIHNhbWVWYWx1ZVplcm9FcXVhbChhLCBiKSB7XG4gICAgcmV0dXJuIGEgPT09IGIgfHwgKCFhICYmICFiICYmIGEgIT09IGEgJiYgYiAhPT0gYik7XG59XG5cbnZhciBQUkVBQ1RfVk5PREUgPSAnX192JztcbnZhciBQUkVBQ1RfT1dORVIgPSAnX19vJztcbnZhciBSRUFDVF9PV05FUiA9ICdfb3duZXInO1xudmFyIGdldE93blByb3BlcnR5RGVzY3JpcHRvciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IsIGtleXMgPSBPYmplY3Qua2V5cztcbi8qKlxuICogV2hldGhlciB0aGUgYXJyYXlzIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xuZnVuY3Rpb24gYXJlQXJyYXlzRXF1YWwoYSwgYiwgc3RhdGUpIHtcbiAgICB2YXIgaW5kZXggPSBhLmxlbmd0aDtcbiAgICBpZiAoYi5sZW5ndGggIT09IGluZGV4KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgd2hpbGUgKGluZGV4LS0gPiAwKSB7XG4gICAgICAgIGlmICghc3RhdGUuZXF1YWxzKGFbaW5kZXhdLCBiW2luZGV4XSwgaW5kZXgsIGluZGV4LCBhLCBiLCBzdGF0ZSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbi8qKlxuICogV2hldGhlciB0aGUgZGF0ZXMgcGFzc2VkIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xuZnVuY3Rpb24gYXJlRGF0ZXNFcXVhbChhLCBiKSB7XG4gICAgcmV0dXJuIHNhbWVWYWx1ZVplcm9FcXVhbChhLmdldFRpbWUoKSwgYi5nZXRUaW1lKCkpO1xufVxuLyoqXG4gKiBXaGV0aGVyIHRoZSBlcnJvcnMgcGFzc2VkIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xuZnVuY3Rpb24gYXJlRXJyb3JzRXF1YWwoYSwgYikge1xuICAgIHJldHVybiAoYS5uYW1lID09PSBiLm5hbWUgJiZcbiAgICAgICAgYS5tZXNzYWdlID09PSBiLm1lc3NhZ2UgJiZcbiAgICAgICAgYS5jYXVzZSA9PT0gYi5jYXVzZSAmJlxuICAgICAgICBhLnN0YWNrID09PSBiLnN0YWNrKTtcbn1cbi8qKlxuICogV2hldGhlciB0aGUgZnVuY3Rpb25zIHBhc3NlZCBhcmUgZXF1YWwgaW4gdmFsdWUuXG4gKi9cbmZ1bmN0aW9uIGFyZUZ1bmN0aW9uc0VxdWFsKGEsIGIpIHtcbiAgICByZXR1cm4gYSA9PT0gYjtcbn1cbi8qKlxuICogV2hldGhlciB0aGUgYE1hcGBzIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xuZnVuY3Rpb24gYXJlTWFwc0VxdWFsKGEsIGIsIHN0YXRlKSB7XG4gICAgdmFyIHNpemUgPSBhLnNpemU7XG4gICAgaWYgKHNpemUgIT09IGIuc2l6ZSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmICghc2l6ZSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgdmFyIG1hdGNoZWRJbmRpY2VzID0gbmV3IEFycmF5KHNpemUpO1xuICAgIHZhciBhSXRlcmFibGUgPSBhLmVudHJpZXMoKTtcbiAgICB2YXIgYVJlc3VsdDtcbiAgICB2YXIgYlJlc3VsdDtcbiAgICB2YXIgaW5kZXggPSAwO1xuICAgIHdoaWxlICgoYVJlc3VsdCA9IGFJdGVyYWJsZS5uZXh0KCkpKSB7XG4gICAgICAgIGlmIChhUmVzdWx0LmRvbmUpIHtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIHZhciBiSXRlcmFibGUgPSBiLmVudHJpZXMoKTtcbiAgICAgICAgdmFyIGhhc01hdGNoID0gZmFsc2U7XG4gICAgICAgIHZhciBtYXRjaEluZGV4ID0gMDtcbiAgICAgICAgd2hpbGUgKChiUmVzdWx0ID0gYkl0ZXJhYmxlLm5leHQoKSkpIHtcbiAgICAgICAgICAgIGlmIChiUmVzdWx0LmRvbmUpIHtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChtYXRjaGVkSW5kaWNlc1ttYXRjaEluZGV4XSkge1xuICAgICAgICAgICAgICAgIG1hdGNoSW5kZXgrKztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHZhciBhRW50cnkgPSBhUmVzdWx0LnZhbHVlO1xuICAgICAgICAgICAgdmFyIGJFbnRyeSA9IGJSZXN1bHQudmFsdWU7XG4gICAgICAgICAgICBpZiAoc3RhdGUuZXF1YWxzKGFFbnRyeVswXSwgYkVudHJ5WzBdLCBpbmRleCwgbWF0Y2hJbmRleCwgYSwgYiwgc3RhdGUpICYmXG4gICAgICAgICAgICAgICAgc3RhdGUuZXF1YWxzKGFFbnRyeVsxXSwgYkVudHJ5WzFdLCBhRW50cnlbMF0sIGJFbnRyeVswXSwgYSwgYiwgc3RhdGUpKSB7XG4gICAgICAgICAgICAgICAgaGFzTWF0Y2ggPSBtYXRjaGVkSW5kaWNlc1ttYXRjaEluZGV4XSA9IHRydWU7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBtYXRjaEluZGV4Kys7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFoYXNNYXRjaCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGluZGV4Kys7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuLyoqXG4gKiBXaGV0aGVyIHRoZSBudW1iZXJzIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xudmFyIGFyZU51bWJlcnNFcXVhbCA9IHNhbWVWYWx1ZVplcm9FcXVhbDtcbi8qKlxuICogV2hldGhlciB0aGUgb2JqZWN0cyBhcmUgZXF1YWwgaW4gdmFsdWUuXG4gKi9cbmZ1bmN0aW9uIGFyZU9iamVjdHNFcXVhbChhLCBiLCBzdGF0ZSkge1xuICAgIHZhciBwcm9wZXJ0aWVzID0ga2V5cyhhKTtcbiAgICB2YXIgaW5kZXggPSBwcm9wZXJ0aWVzLmxlbmd0aDtcbiAgICBpZiAoa2V5cyhiKS5sZW5ndGggIT09IGluZGV4KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgLy8gRGVjcmVtZW50aW5nIGB3aGlsZWAgc2hvd2VkIGZhc3RlciByZXN1bHRzIHRoYW4gZWl0aGVyIGluY3JlbWVudGluZyBvclxuICAgIC8vIGRlY3JlbWVudGluZyBgZm9yYCBsb29wIGFuZCB0aGFuIGFuIGluY3JlbWVudGluZyBgd2hpbGVgIGxvb3AuIERlY2xhcmF0aXZlXG4gICAgLy8gbWV0aG9kcyBsaWtlIGBzb21lYCAvIGBldmVyeWAgd2VyZSBub3QgdXNlZCB0byBhdm9pZCBpbmN1cnJpbmcgdGhlIGdhcmJhZ2VcbiAgICAvLyBjb3N0IG9mIGFub255bW91cyBjYWxsYmFja3MuXG4gICAgd2hpbGUgKGluZGV4LS0gPiAwKSB7XG4gICAgICAgIGlmICghaXNQcm9wZXJ0eUVxdWFsKGEsIGIsIHN0YXRlLCBwcm9wZXJ0aWVzW2luZGV4XSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbi8qKlxuICogV2hldGhlciB0aGUgb2JqZWN0cyBhcmUgZXF1YWwgaW4gdmFsdWUgd2l0aCBzdHJpY3QgcHJvcGVydHkgY2hlY2tpbmcuXG4gKi9cbmZ1bmN0aW9uIGFyZU9iamVjdHNFcXVhbFN0cmljdChhLCBiLCBzdGF0ZSkge1xuICAgIHZhciBwcm9wZXJ0aWVzID0gZ2V0U3RyaWN0UHJvcGVydGllcyhhKTtcbiAgICB2YXIgaW5kZXggPSBwcm9wZXJ0aWVzLmxlbmd0aDtcbiAgICBpZiAoZ2V0U3RyaWN0UHJvcGVydGllcyhiKS5sZW5ndGggIT09IGluZGV4KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgdmFyIHByb3BlcnR5O1xuICAgIHZhciBkZXNjcmlwdG9yQTtcbiAgICB2YXIgZGVzY3JpcHRvckI7XG4gICAgLy8gRGVjcmVtZW50aW5nIGB3aGlsZWAgc2hvd2VkIGZhc3RlciByZXN1bHRzIHRoYW4gZWl0aGVyIGluY3JlbWVudGluZyBvclxuICAgIC8vIGRlY3JlbWVudGluZyBgZm9yYCBsb29wIGFuZCB0aGFuIGFuIGluY3JlbWVudGluZyBgd2hpbGVgIGxvb3AuIERlY2xhcmF0aXZlXG4gICAgLy8gbWV0aG9kcyBsaWtlIGBzb21lYCAvIGBldmVyeWAgd2VyZSBub3QgdXNlZCB0byBhdm9pZCBpbmN1cnJpbmcgdGhlIGdhcmJhZ2VcbiAgICAvLyBjb3N0IG9mIGFub255bW91cyBjYWxsYmFja3MuXG4gICAgd2hpbGUgKGluZGV4LS0gPiAwKSB7XG4gICAgICAgIHByb3BlcnR5ID0gcHJvcGVydGllc1tpbmRleF07XG4gICAgICAgIGlmICghaXNQcm9wZXJ0eUVxdWFsKGEsIGIsIHN0YXRlLCBwcm9wZXJ0eSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBkZXNjcmlwdG9yQSA9IGdldE93blByb3BlcnR5RGVzY3JpcHRvcihhLCBwcm9wZXJ0eSk7XG4gICAgICAgIGRlc2NyaXB0b3JCID0gZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGIsIHByb3BlcnR5KTtcbiAgICAgICAgaWYgKChkZXNjcmlwdG9yQSB8fCBkZXNjcmlwdG9yQikgJiZcbiAgICAgICAgICAgICghZGVzY3JpcHRvckEgfHxcbiAgICAgICAgICAgICAgICAhZGVzY3JpcHRvckIgfHxcbiAgICAgICAgICAgICAgICBkZXNjcmlwdG9yQS5jb25maWd1cmFibGUgIT09IGRlc2NyaXB0b3JCLmNvbmZpZ3VyYWJsZSB8fFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0b3JBLmVudW1lcmFibGUgIT09IGRlc2NyaXB0b3JCLmVudW1lcmFibGUgfHxcbiAgICAgICAgICAgICAgICBkZXNjcmlwdG9yQS53cml0YWJsZSAhPT0gZGVzY3JpcHRvckIud3JpdGFibGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG4vKipcbiAqIFdoZXRoZXIgdGhlIHByaW1pdGl2ZSB3cmFwcGVycyBwYXNzZWQgYXJlIGVxdWFsIGluIHZhbHVlLlxuICovXG5mdW5jdGlvbiBhcmVQcmltaXRpdmVXcmFwcGVyc0VxdWFsKGEsIGIpIHtcbiAgICByZXR1cm4gc2FtZVZhbHVlWmVyb0VxdWFsKGEudmFsdWVPZigpLCBiLnZhbHVlT2YoKSk7XG59XG4vKipcbiAqIFdoZXRoZXIgdGhlIHJlZ2V4cHMgcGFzc2VkIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xuZnVuY3Rpb24gYXJlUmVnRXhwc0VxdWFsKGEsIGIpIHtcbiAgICByZXR1cm4gYS5zb3VyY2UgPT09IGIuc291cmNlICYmIGEuZmxhZ3MgPT09IGIuZmxhZ3M7XG59XG4vKipcbiAqIFdoZXRoZXIgdGhlIGBTZXRgcyBhcmUgZXF1YWwgaW4gdmFsdWUuXG4gKi9cbmZ1bmN0aW9uIGFyZVNldHNFcXVhbChhLCBiLCBzdGF0ZSkge1xuICAgIHZhciBzaXplID0gYS5zaXplO1xuICAgIGlmIChzaXplICE9PSBiLnNpemUpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoIXNpemUpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHZhciBtYXRjaGVkSW5kaWNlcyA9IG5ldyBBcnJheShzaXplKTtcbiAgICB2YXIgYUl0ZXJhYmxlID0gYS52YWx1ZXMoKTtcbiAgICB2YXIgYVJlc3VsdDtcbiAgICB2YXIgYlJlc3VsdDtcbiAgICB3aGlsZSAoKGFSZXN1bHQgPSBhSXRlcmFibGUubmV4dCgpKSkge1xuICAgICAgICBpZiAoYVJlc3VsdC5kb25lKSB7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICB2YXIgYkl0ZXJhYmxlID0gYi52YWx1ZXMoKTtcbiAgICAgICAgdmFyIGhhc01hdGNoID0gZmFsc2U7XG4gICAgICAgIHZhciBtYXRjaEluZGV4ID0gMDtcbiAgICAgICAgd2hpbGUgKChiUmVzdWx0ID0gYkl0ZXJhYmxlLm5leHQoKSkpIHtcbiAgICAgICAgICAgIGlmIChiUmVzdWx0LmRvbmUpIHtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghbWF0Y2hlZEluZGljZXNbbWF0Y2hJbmRleF0gJiZcbiAgICAgICAgICAgICAgICBzdGF0ZS5lcXVhbHMoYVJlc3VsdC52YWx1ZSwgYlJlc3VsdC52YWx1ZSwgYVJlc3VsdC52YWx1ZSwgYlJlc3VsdC52YWx1ZSwgYSwgYiwgc3RhdGUpKSB7XG4gICAgICAgICAgICAgICAgaGFzTWF0Y2ggPSBtYXRjaGVkSW5kaWNlc1ttYXRjaEluZGV4XSA9IHRydWU7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBtYXRjaEluZGV4Kys7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFoYXNNYXRjaCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuLyoqXG4gKiBXaGV0aGVyIHRoZSBUeXBlZEFycmF5IGluc3RhbmNlcyBhcmUgZXF1YWwgaW4gdmFsdWUuXG4gKi9cbmZ1bmN0aW9uIGFyZVR5cGVkQXJyYXlzRXF1YWwoYSwgYikge1xuICAgIHZhciBpbmRleCA9IGEubGVuZ3RoO1xuICAgIGlmIChiLmxlbmd0aCAhPT0gaW5kZXgpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICB3aGlsZSAoaW5kZXgtLSA+IDApIHtcbiAgICAgICAgaWYgKGFbaW5kZXhdICE9PSBiW2luZGV4XSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuLyoqXG4gKiBXaGV0aGVyIHRoZSBVUkwgaW5zdGFuY2VzIGFyZSBlcXVhbCBpbiB2YWx1ZS5cbiAqL1xuZnVuY3Rpb24gYXJlVXJsc0VxdWFsKGEsIGIpIHtcbiAgICByZXR1cm4gKGEuaG9zdG5hbWUgPT09IGIuaG9zdG5hbWUgJiZcbiAgICAgICAgYS5wYXRobmFtZSA9PT0gYi5wYXRobmFtZSAmJlxuICAgICAgICBhLnByb3RvY29sID09PSBiLnByb3RvY29sICYmXG4gICAgICAgIGEucG9ydCA9PT0gYi5wb3J0ICYmXG4gICAgICAgIGEuaGFzaCA9PT0gYi5oYXNoICYmXG4gICAgICAgIGEudXNlcm5hbWUgPT09IGIudXNlcm5hbWUgJiZcbiAgICAgICAgYS5wYXNzd29yZCA9PT0gYi5wYXNzd29yZCk7XG59XG5mdW5jdGlvbiBpc1Byb3BlcnR5RXF1YWwoYSwgYiwgc3RhdGUsIHByb3BlcnR5KSB7XG4gICAgaWYgKChwcm9wZXJ0eSA9PT0gUkVBQ1RfT1dORVIgfHxcbiAgICAgICAgcHJvcGVydHkgPT09IFBSRUFDVF9PV05FUiB8fFxuICAgICAgICBwcm9wZXJ0eSA9PT0gUFJFQUNUX1ZOT0RFKSAmJlxuICAgICAgICAoYS4kJHR5cGVvZiB8fCBiLiQkdHlwZW9mKSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIChoYXNPd24oYiwgcHJvcGVydHkpICYmXG4gICAgICAgIHN0YXRlLmVxdWFscyhhW3Byb3BlcnR5XSwgYltwcm9wZXJ0eV0sIHByb3BlcnR5LCBwcm9wZXJ0eSwgYSwgYiwgc3RhdGUpKTtcbn1cblxudmFyIEFSR1VNRU5UU19UQUcgPSAnW29iamVjdCBBcmd1bWVudHNdJztcbnZhciBCT09MRUFOX1RBRyA9ICdbb2JqZWN0IEJvb2xlYW5dJztcbnZhciBEQVRFX1RBRyA9ICdbb2JqZWN0IERhdGVdJztcbnZhciBFUlJPUl9UQUcgPSAnW29iamVjdCBFcnJvcl0nO1xudmFyIE1BUF9UQUcgPSAnW29iamVjdCBNYXBdJztcbnZhciBOVU1CRVJfVEFHID0gJ1tvYmplY3QgTnVtYmVyXSc7XG52YXIgT0JKRUNUX1RBRyA9ICdbb2JqZWN0IE9iamVjdF0nO1xudmFyIFJFR19FWFBfVEFHID0gJ1tvYmplY3QgUmVnRXhwXSc7XG52YXIgU0VUX1RBRyA9ICdbb2JqZWN0IFNldF0nO1xudmFyIFNUUklOR19UQUcgPSAnW29iamVjdCBTdHJpbmddJztcbnZhciBVUkxfVEFHID0gJ1tvYmplY3QgVVJMXSc7XG52YXIgaXNBcnJheSA9IEFycmF5LmlzQXJyYXk7XG52YXIgaXNUeXBlZEFycmF5ID0gdHlwZW9mIEFycmF5QnVmZmVyID09PSAnZnVuY3Rpb24nICYmIEFycmF5QnVmZmVyLmlzVmlld1xuICAgID8gQXJyYXlCdWZmZXIuaXNWaWV3XG4gICAgOiBudWxsO1xudmFyIGFzc2lnbiA9IE9iamVjdC5hc3NpZ247XG52YXIgZ2V0VGFnID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsLmJpbmQoT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZyk7XG4vKipcbiAqIENyZWF0ZSBhIGNvbXBhcmF0b3IgbWV0aG9kIGJhc2VkIG9uIHRoZSB0eXBlLXNwZWNpZmljIGVxdWFsaXR5IGNvbXBhcmF0b3JzIHBhc3NlZC5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlRXF1YWxpdHlDb21wYXJhdG9yKF9hKSB7XG4gICAgdmFyIGFyZUFycmF5c0VxdWFsID0gX2EuYXJlQXJyYXlzRXF1YWwsIGFyZURhdGVzRXF1YWwgPSBfYS5hcmVEYXRlc0VxdWFsLCBhcmVFcnJvcnNFcXVhbCA9IF9hLmFyZUVycm9yc0VxdWFsLCBhcmVGdW5jdGlvbnNFcXVhbCA9IF9hLmFyZUZ1bmN0aW9uc0VxdWFsLCBhcmVNYXBzRXF1YWwgPSBfYS5hcmVNYXBzRXF1YWwsIGFyZU51bWJlcnNFcXVhbCA9IF9hLmFyZU51bWJlcnNFcXVhbCwgYXJlT2JqZWN0c0VxdWFsID0gX2EuYXJlT2JqZWN0c0VxdWFsLCBhcmVQcmltaXRpdmVXcmFwcGVyc0VxdWFsID0gX2EuYXJlUHJpbWl0aXZlV3JhcHBlcnNFcXVhbCwgYXJlUmVnRXhwc0VxdWFsID0gX2EuYXJlUmVnRXhwc0VxdWFsLCBhcmVTZXRzRXF1YWwgPSBfYS5hcmVTZXRzRXF1YWwsIGFyZVR5cGVkQXJyYXlzRXF1YWwgPSBfYS5hcmVUeXBlZEFycmF5c0VxdWFsLCBhcmVVcmxzRXF1YWwgPSBfYS5hcmVVcmxzRXF1YWw7XG4gICAgLyoqXG4gICAgICogY29tcGFyZSB0aGUgdmFsdWUgb2YgdGhlIHR3byBvYmplY3RzIGFuZCByZXR1cm4gdHJ1ZSBpZiB0aGV5IGFyZSBlcXVpdmFsZW50IGluIHZhbHVlc1xuICAgICAqL1xuICAgIHJldHVybiBmdW5jdGlvbiBjb21wYXJhdG9yKGEsIGIsIHN0YXRlKSB7XG4gICAgICAgIC8vIElmIHRoZSBpdGVtcyBhcmUgc3RyaWN0bHkgZXF1YWwsIG5vIG5lZWQgdG8gZG8gYSB2YWx1ZSBjb21wYXJpc29uLlxuICAgICAgICBpZiAoYSA9PT0gYikge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8gSWYgZWl0aGVyIG9mIHRoZSBpdGVtcyBhcmUgbnVsbGlzaCBhbmQgZmFpbCB0aGUgc3RyaWN0bHkgZXF1YWwgY2hlY2tcbiAgICAgICAgLy8gYWJvdmUsIHRoZW4gdGhleSBtdXN0IGJlIHVuZXF1YWwuXG4gICAgICAgIGlmIChhID09IG51bGwgfHwgYiA9PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIHR5cGUgPSB0eXBlb2YgYTtcbiAgICAgICAgaWYgKHR5cGUgIT09IHR5cGVvZiBiKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGUgIT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICBpZiAodHlwZSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXJlTnVtYmVyc0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0eXBlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFyZUZ1bmN0aW9uc0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIGEgcHJpbWl0aXZlIHZhbHVlIHRoYXQgaXMgbm90IHN0cmljdGx5IGVxdWFsLCBpdCBtdXN0IGJlIHVuZXF1YWwuXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGNvbnN0cnVjdG9yID0gYS5jb25zdHJ1Y3RvcjtcbiAgICAgICAgLy8gQ2hlY2tzIGFyZSBsaXN0ZWQgaW4gb3JkZXIgb2YgY29tbW9uYWxpdHkgb2YgdXNlLWNhc2U6XG4gICAgICAgIC8vICAgMS4gQ29tbW9uIGNvbXBsZXggb2JqZWN0IHR5cGVzIChwbGFpbiBvYmplY3QsIGFycmF5KVxuICAgICAgICAvLyAgIDIuIENvbW1vbiBkYXRhIHZhbHVlcyAoZGF0ZSwgcmVnZXhwKVxuICAgICAgICAvLyAgIDMuIExlc3MtY29tbW9uIGNvbXBsZXggb2JqZWN0IHR5cGVzIChtYXAsIHNldClcbiAgICAgICAgLy8gICA0LiBMZXNzLWNvbW1vbiBkYXRhIHZhbHVlcyAocHJvbWlzZSwgcHJpbWl0aXZlIHdyYXBwZXJzKVxuICAgICAgICAvLyBJbmhlcmVudGx5IHRoaXMgaXMgYm90aCBzdWJqZWN0aXZlIGFuZCBhc3N1bXB0aXZlLCBob3dldmVyXG4gICAgICAgIC8vIHdoZW4gcmV2aWV3aW5nIGNvbXBhcmFibGUgbGlicmFyaWVzIGluIHRoZSB3aWxkIHRoaXMgb3JkZXJcbiAgICAgICAgLy8gYXBwZWFycyB0byBiZSBnZW5lcmFsbHkgY29uc2lzdGVudC5cbiAgICAgICAgLy8gQ29uc3RydWN0b3JzIHNob3VsZCBtYXRjaCwgb3RoZXJ3aXNlIHRoZXJlIGlzIHBvdGVudGlhbCBmb3IgZmFsc2UgcG9zaXRpdmVzXG4gICAgICAgIC8vIGJldHdlZW4gY2xhc3MgYW5kIHN1YmNsYXNzIG9yIGN1c3RvbSBvYmplY3QgYW5kIFBPSk8uXG4gICAgICAgIGlmIChjb25zdHJ1Y3RvciAhPT0gYi5jb25zdHJ1Y3Rvcikge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIC8vIGBpc1BsYWluT2JqZWN0YCBvbmx5IGNoZWNrcyBhZ2FpbnN0IHRoZSBvYmplY3QncyBvd24gcmVhbG0uIENyb3NzLXJlYWxtXG4gICAgICAgIC8vIGNvbXBhcmlzb25zIGFyZSByYXJlLCBhbmQgd2lsbCBiZSBoYW5kbGVkIGluIHRoZSB1bHRpbWF0ZSBmYWxsYmFjaywgc29cbiAgICAgICAgLy8gd2UgY2FuIGF2b2lkIGNhcHR1cmluZyB0aGUgc3RyaW5nIHRhZy5cbiAgICAgICAgaWYgKGNvbnN0cnVjdG9yID09PSBPYmplY3QpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVPYmplY3RzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIC8vIGBpc0FycmF5KClgIHdvcmtzIG9uIHN1YmNsYXNzZXMgYW5kIGlzIGNyb3NzLXJlYWxtLCBzbyB3ZSBjYW4gYXZvaWQgY2FwdHVyaW5nXG4gICAgICAgIC8vIHRoZSBzdHJpbmcgdGFnIG9yIGRvaW5nIGFuIGBpbnN0YW5jZW9mYCBjaGVjay5cbiAgICAgICAgaWYgKGlzQXJyYXkoYSkpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVBcnJheXNFcXVhbChhLCBiLCBzdGF0ZSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gYGlzVHlwZWRBcnJheSgpYCB3b3JrcyBvbiBhbGwgcG9zc2libGUgVHlwZWRBcnJheSBjbGFzc2VzLCBzbyB3ZSBjYW4gYXZvaWRcbiAgICAgICAgLy8gY2FwdHVyaW5nIHRoZSBzdHJpbmcgdGFnIG9yIGNvbXBhcmluZyBhZ2FpbnN0IGFsbCBwb3NzaWJsZSBjb25zdHJ1Y3RvcnMuXG4gICAgICAgIGlmIChpc1R5cGVkQXJyYXkgIT0gbnVsbCAmJiBpc1R5cGVkQXJyYXkoYSkpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVUeXBlZEFycmF5c0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBUcnkgdG8gZmFzdC1wYXRoIGVxdWFsaXR5IGNoZWNrcyBmb3Igb3RoZXIgY29tcGxleCBvYmplY3QgdHlwZXMgaW4gdGhlXG4gICAgICAgIC8vIHNhbWUgcmVhbG0gdG8gYXZvaWQgY2FwdHVyaW5nIHRoZSBzdHJpbmcgdGFnLiBTdHJpY3QgZXF1YWxpdHkgaXMgdXNlZFxuICAgICAgICAvLyBpbnN0ZWFkIG9mIGBpbnN0YW5jZW9mYCBiZWNhdXNlIGl0IGlzIG1vcmUgcGVyZm9ybWFudCBmb3IgdGhlIGNvbW1vblxuICAgICAgICAvLyB1c2UtY2FzZS4gSWYgc29tZW9uZSBpcyBzdWJjbGFzc2luZyBhIG5hdGl2ZSBjbGFzcywgaXQgd2lsbCBiZSBoYW5kbGVkXG4gICAgICAgIC8vIHdpdGggdGhlIHN0cmluZyB0YWcgY29tcGFyaXNvbi5cbiAgICAgICAgaWYgKGNvbnN0cnVjdG9yID09PSBEYXRlKSB7XG4gICAgICAgICAgICByZXR1cm4gYXJlRGF0ZXNFcXVhbChhLCBiLCBzdGF0ZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNvbnN0cnVjdG9yID09PSBSZWdFeHApIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVSZWdFeHBzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChjb25zdHJ1Y3RvciA9PT0gTWFwKSB7XG4gICAgICAgICAgICByZXR1cm4gYXJlTWFwc0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY29uc3RydWN0b3IgPT09IFNldCkge1xuICAgICAgICAgICAgcmV0dXJuIGFyZVNldHNFcXVhbChhLCBiLCBzdGF0ZSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gU2luY2UgdGhpcyBpcyBhIGN1c3RvbSBvYmplY3QsIGNhcHR1cmUgdGhlIHN0cmluZyB0YWcgdG8gZGV0ZXJtaW5nIGl0cyB0eXBlLlxuICAgICAgICAvLyBUaGlzIGlzIHJlYXNvbmFibHkgcGVyZm9ybWFudCBpbiBtb2Rlcm4gZW52aXJvbm1lbnRzIGxpa2UgdjggYW5kIFNwaWRlck1vbmtleS5cbiAgICAgICAgdmFyIHRhZyA9IGdldFRhZyhhKTtcbiAgICAgICAgaWYgKHRhZyA9PT0gREFURV9UQUcpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVEYXRlc0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBGb3IgUmVnRXhwLCB0aGUgcHJvcGVydGllcyBhcmUgbm90IGVudW1lcmFibGUsIGFuZCB0aGVyZWZvcmUgd2lsbCBnaXZlIGZhbHNlIHBvc2l0aXZlcyBpZlxuICAgICAgICAvLyB0ZXN0ZWQgbGlrZSBhIHN0YW5kYXJkIG9iamVjdC5cbiAgICAgICAgaWYgKHRhZyA9PT0gUkVHX0VYUF9UQUcpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVSZWdFeHBzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0YWcgPT09IE1BUF9UQUcpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVNYXBzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0YWcgPT09IFNFVF9UQUcpIHtcbiAgICAgICAgICAgIHJldHVybiBhcmVTZXRzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0YWcgPT09IE9CSkVDVF9UQUcpIHtcbiAgICAgICAgICAgIC8vIFRoZSBleGNlcHRpb24gZm9yIHZhbHVlIGNvbXBhcmlzb24gaXMgY3VzdG9tIGBQcm9taXNlYC1saWtlIGNsYXNzIGluc3RhbmNlcy4gVGhlc2Ugc2hvdWxkXG4gICAgICAgICAgICAvLyBiZSB0cmVhdGVkIHRoZSBzYW1lIGFzIHN0YW5kYXJkIGBQcm9taXNlYCBvYmplY3RzLCB3aGljaCBtZWFucyBzdHJpY3QgZXF1YWxpdHksIGFuZCBpZlxuICAgICAgICAgICAgLy8gaXQgcmVhY2hlcyB0aGlzIHBvaW50IHRoZW4gdGhhdCBzdHJpY3QgZXF1YWxpdHkgY29tcGFyaXNvbiBoYXMgYWxyZWFkeSBmYWlsZWQuXG4gICAgICAgICAgICByZXR1cm4gKHR5cGVvZiBhLnRoZW4gIT09ICdmdW5jdGlvbicgJiZcbiAgICAgICAgICAgICAgICB0eXBlb2YgYi50aGVuICE9PSAnZnVuY3Rpb24nICYmXG4gICAgICAgICAgICAgICAgYXJlT2JqZWN0c0VxdWFsKGEsIGIsIHN0YXRlKSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gSWYgYSBVUkwgdGFnLCBpdCBzaG91bGQgYmUgdGVzdGVkIGV4cGxpY2l0bHkuIExpa2UgUmVnRXhwLCB0aGUgcHJvcGVydGllcyBhcmUgbm90XG4gICAgICAgIC8vIGVudW1lcmFibGUsIGFuZCB0aGVyZWZvcmUgd2lsbCBnaXZlIGZhbHNlIHBvc2l0aXZlcyBpZiB0ZXN0ZWQgbGlrZSBhIHN0YW5kYXJkIG9iamVjdC5cbiAgICAgICAgaWYgKHRhZyA9PT0gVVJMX1RBRykge1xuICAgICAgICAgICAgcmV0dXJuIGFyZVVybHNFcXVhbChhLCBiLCBzdGF0ZSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gSWYgYW4gZXJyb3IgdGFnLCBpdCBzaG91bGQgYmUgdGVzdGVkIGV4cGxpY2l0bHkuIExpa2UgUmVnRXhwLCB0aGUgcHJvcGVydGllcyBhcmUgbm90XG4gICAgICAgIC8vIGVudW1lcmFibGUsIGFuZCB0aGVyZWZvcmUgd2lsbCBnaXZlIGZhbHNlIHBvc2l0aXZlcyBpZiB0ZXN0ZWQgbGlrZSBhIHN0YW5kYXJkIG9iamVjdC5cbiAgICAgICAgaWYgKHRhZyA9PT0gRVJST1JfVEFHKSB7XG4gICAgICAgICAgICByZXR1cm4gYXJlRXJyb3JzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIGFuIGFyZ3VtZW50cyB0YWcsIGl0IHNob3VsZCBiZSB0cmVhdGVkIGFzIGEgc3RhbmRhcmQgb2JqZWN0LlxuICAgICAgICBpZiAodGFnID09PSBBUkdVTUVOVFNfVEFHKSB7XG4gICAgICAgICAgICByZXR1cm4gYXJlT2JqZWN0c0VxdWFsKGEsIGIsIHN0YXRlKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBBcyB0aGUgcGVudWx0aW1hdGUgZmFsbGJhY2ssIGNoZWNrIGlmIHRoZSB2YWx1ZXMgcGFzc2VkIGFyZSBwcmltaXRpdmUgd3JhcHBlcnMuIFRoaXNcbiAgICAgICAgLy8gaXMgdmVyeSByYXJlIGluIG1vZGVybiBKUywgd2hpY2ggaXMgd2h5IGl0IGlzIGRlcHJpb3JpdGl6ZWQgY29tcGFyZWQgdG8gYWxsIG90aGVyIG9iamVjdFxuICAgICAgICAvLyB0eXBlcy5cbiAgICAgICAgaWYgKHRhZyA9PT0gQk9PTEVBTl9UQUcgfHwgdGFnID09PSBOVU1CRVJfVEFHIHx8IHRhZyA9PT0gU1RSSU5HX1RBRykge1xuICAgICAgICAgICAgcmV0dXJuIGFyZVByaW1pdGl2ZVdyYXBwZXJzRXF1YWwoYSwgYiwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIG5vdCBtYXRjaGluZyBhbnkgdGFncyB0aGF0IHJlcXVpcmUgYSBzcGVjaWZpYyB0eXBlIG9mIGNvbXBhcmlzb24sIHRoZW4gd2UgaGFyZC1jb2RlIGZhbHNlIGJlY2F1c2VcbiAgICAgICAgLy8gdGhlIG9ubHkgdGhpbmcgcmVtYWluaW5nIGlzIHN0cmljdCBlcXVhbGl0eSwgd2hpY2ggaGFzIGFscmVhZHkgYmVlbiBjb21wYXJlZC4gVGhpcyBpcyBmb3IgYSBmZXcgcmVhc29uczpcbiAgICAgICAgLy8gICAtIENlcnRhaW4gdHlwZXMgdGhhdCBjYW5ub3QgYmUgaW50cm9zcGVjdGVkIChlLmcuLCBgV2Vha01hcGApLiBGb3IgdGhlc2UgdHlwZXMsIHRoaXMgaXMgdGhlIG9ubHlcbiAgICAgICAgLy8gICAgIGNvbXBhcmlzb24gdGhhdCBjYW4gYmUgbWFkZS5cbiAgICAgICAgLy8gICAtIEZvciB0eXBlcyB0aGF0IGNhbiBiZSBpbnRyb3NwZWN0ZWQsIGJ1dCByYXJlbHkgaGF2ZSByZXF1aXJlbWVudHMgdG8gYmUgY29tcGFyZWRcbiAgICAgICAgLy8gICAgIChgQXJyYXlCdWZmZXJgLCBgRGF0YVZpZXdgLCBldGMuKSwgdGhlIGNvc3QgaXMgYXZvaWRlZCB0byBwcmlvcml0aXplIHRoZSBjb21tb25cbiAgICAgICAgLy8gICAgIHVzZS1jYXNlcyAobWF5IGJlIGluY2x1ZGVkIGluIGEgZnV0dXJlIHJlbGVhc2UsIGlmIHJlcXVlc3RlZCBlbm91Z2gpLlxuICAgICAgICAvLyAgIC0gRm9yIHR5cGVzIHRoYXQgY2FuIGJlIGludHJvc3BlY3RlZCBidXQgZG8gbm90IGhhdmUgYW4gb2JqZWN0aXZlIGRlZmluaXRpb24gb2Ygd2hhdFxuICAgICAgICAvLyAgICAgZXF1YWxpdHkgaXMgKGBFcnJvcmAsIGV0Yy4pLCB0aGUgc3ViamVjdGl2ZSBkZWNpc2lvbiBpcyB0byBiZSBjb25zZXJ2YXRpdmUgYW5kIHN0cmljdGx5IGNvbXBhcmUuXG4gICAgICAgIC8vIEluIGFsbCBjYXNlcywgdGhlc2UgZGVjaXNpb25zIHNob3VsZCBiZSByZWV2YWx1YXRlZCBiYXNlZCBvbiBjaGFuZ2VzIHRvIHRoZSBsYW5ndWFnZSBhbmRcbiAgICAgICAgLy8gY29tbW9uIGRldmVsb3BtZW50IHByYWN0aWNlcy5cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH07XG59XG4vKipcbiAqIENyZWF0ZSB0aGUgY29uZmlndXJhdGlvbiBvYmplY3QgdXNlZCBmb3IgYnVpbGRpbmcgY29tcGFyYXRvcnMuXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZUVxdWFsaXR5Q29tcGFyYXRvckNvbmZpZyhfYSkge1xuICAgIHZhciBjaXJjdWxhciA9IF9hLmNpcmN1bGFyLCBjcmVhdGVDdXN0b21Db25maWcgPSBfYS5jcmVhdGVDdXN0b21Db25maWcsIHN0cmljdCA9IF9hLnN0cmljdDtcbiAgICB2YXIgY29uZmlnID0ge1xuICAgICAgICBhcmVBcnJheXNFcXVhbDogc3RyaWN0XG4gICAgICAgICAgICA/IGFyZU9iamVjdHNFcXVhbFN0cmljdFxuICAgICAgICAgICAgOiBhcmVBcnJheXNFcXVhbCxcbiAgICAgICAgYXJlRGF0ZXNFcXVhbDogYXJlRGF0ZXNFcXVhbCxcbiAgICAgICAgYXJlRXJyb3JzRXF1YWw6IGFyZUVycm9yc0VxdWFsLFxuICAgICAgICBhcmVGdW5jdGlvbnNFcXVhbDogYXJlRnVuY3Rpb25zRXF1YWwsXG4gICAgICAgIGFyZU1hcHNFcXVhbDogc3RyaWN0XG4gICAgICAgICAgICA/IGNvbWJpbmVDb21wYXJhdG9ycyhhcmVNYXBzRXF1YWwsIGFyZU9iamVjdHNFcXVhbFN0cmljdClcbiAgICAgICAgICAgIDogYXJlTWFwc0VxdWFsLFxuICAgICAgICBhcmVOdW1iZXJzRXF1YWw6IGFyZU51bWJlcnNFcXVhbCxcbiAgICAgICAgYXJlT2JqZWN0c0VxdWFsOiBzdHJpY3RcbiAgICAgICAgICAgID8gYXJlT2JqZWN0c0VxdWFsU3RyaWN0XG4gICAgICAgICAgICA6IGFyZU9iamVjdHNFcXVhbCxcbiAgICAgICAgYXJlUHJpbWl0aXZlV3JhcHBlcnNFcXVhbDogYXJlUHJpbWl0aXZlV3JhcHBlcnNFcXVhbCxcbiAgICAgICAgYXJlUmVnRXhwc0VxdWFsOiBhcmVSZWdFeHBzRXF1YWwsXG4gICAgICAgIGFyZVNldHNFcXVhbDogc3RyaWN0XG4gICAgICAgICAgICA/IGNvbWJpbmVDb21wYXJhdG9ycyhhcmVTZXRzRXF1YWwsIGFyZU9iamVjdHNFcXVhbFN0cmljdClcbiAgICAgICAgICAgIDogYXJlU2V0c0VxdWFsLFxuICAgICAgICBhcmVUeXBlZEFycmF5c0VxdWFsOiBzdHJpY3RcbiAgICAgICAgICAgID8gYXJlT2JqZWN0c0VxdWFsU3RyaWN0XG4gICAgICAgICAgICA6IGFyZVR5cGVkQXJyYXlzRXF1YWwsXG4gICAgICAgIGFyZVVybHNFcXVhbDogYXJlVXJsc0VxdWFsLFxuICAgIH07XG4gICAgaWYgKGNyZWF0ZUN1c3RvbUNvbmZpZykge1xuICAgICAgICBjb25maWcgPSBhc3NpZ24oe30sIGNvbmZpZywgY3JlYXRlQ3VzdG9tQ29uZmlnKGNvbmZpZykpO1xuICAgIH1cbiAgICBpZiAoY2lyY3VsYXIpIHtcbiAgICAgICAgdmFyIGFyZUFycmF5c0VxdWFsJDEgPSBjcmVhdGVJc0NpcmN1bGFyKGNvbmZpZy5hcmVBcnJheXNFcXVhbCk7XG4gICAgICAgIHZhciBhcmVNYXBzRXF1YWwkMSA9IGNyZWF0ZUlzQ2lyY3VsYXIoY29uZmlnLmFyZU1hcHNFcXVhbCk7XG4gICAgICAgIHZhciBhcmVPYmplY3RzRXF1YWwkMSA9IGNyZWF0ZUlzQ2lyY3VsYXIoY29uZmlnLmFyZU9iamVjdHNFcXVhbCk7XG4gICAgICAgIHZhciBhcmVTZXRzRXF1YWwkMSA9IGNyZWF0ZUlzQ2lyY3VsYXIoY29uZmlnLmFyZVNldHNFcXVhbCk7XG4gICAgICAgIGNvbmZpZyA9IGFzc2lnbih7fSwgY29uZmlnLCB7XG4gICAgICAgICAgICBhcmVBcnJheXNFcXVhbDogYXJlQXJyYXlzRXF1YWwkMSxcbiAgICAgICAgICAgIGFyZU1hcHNFcXVhbDogYXJlTWFwc0VxdWFsJDEsXG4gICAgICAgICAgICBhcmVPYmplY3RzRXF1YWw6IGFyZU9iamVjdHNFcXVhbCQxLFxuICAgICAgICAgICAgYXJlU2V0c0VxdWFsOiBhcmVTZXRzRXF1YWwkMSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiBjb25maWc7XG59XG4vKipcbiAqIERlZmF1bHQgZXF1YWxpdHkgY29tcGFyYXRvciBwYXNzLXRocm91Z2gsIHVzZWQgYXMgdGhlIHN0YW5kYXJkIGBpc0VxdWFsYCBjcmVhdG9yIGZvclxuICogdXNlIGluc2lkZSB0aGUgYnVpbHQgY29tcGFyYXRvci5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlSW50ZXJuYWxFcXVhbGl0eUNvbXBhcmF0b3IoY29tcGFyZSkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoYSwgYiwgX2luZGV4T3JLZXlBLCBfaW5kZXhPcktleUIsIF9wYXJlbnRBLCBfcGFyZW50Qiwgc3RhdGUpIHtcbiAgICAgICAgcmV0dXJuIGNvbXBhcmUoYSwgYiwgc3RhdGUpO1xuICAgIH07XG59XG4vKipcbiAqIENyZWF0ZSB0aGUgYGlzRXF1YWxgIGZ1bmN0aW9uIHVzZWQgYnkgdGhlIGNvbnN1bWluZyBhcHBsaWNhdGlvbi5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlSXNFcXVhbChfYSkge1xuICAgIHZhciBjaXJjdWxhciA9IF9hLmNpcmN1bGFyLCBjb21wYXJhdG9yID0gX2EuY29tcGFyYXRvciwgY3JlYXRlU3RhdGUgPSBfYS5jcmVhdGVTdGF0ZSwgZXF1YWxzID0gX2EuZXF1YWxzLCBzdHJpY3QgPSBfYS5zdHJpY3Q7XG4gICAgaWYgKGNyZWF0ZVN0YXRlKSB7XG4gICAgICAgIHJldHVybiBmdW5jdGlvbiBpc0VxdWFsKGEsIGIpIHtcbiAgICAgICAgICAgIHZhciBfYSA9IGNyZWF0ZVN0YXRlKCksIF9iID0gX2EuY2FjaGUsIGNhY2hlID0gX2IgPT09IHZvaWQgMCA/IGNpcmN1bGFyID8gbmV3IFdlYWtNYXAoKSA6IHVuZGVmaW5lZCA6IF9iLCBtZXRhID0gX2EubWV0YTtcbiAgICAgICAgICAgIHJldHVybiBjb21wYXJhdG9yKGEsIGIsIHtcbiAgICAgICAgICAgICAgICBjYWNoZTogY2FjaGUsXG4gICAgICAgICAgICAgICAgZXF1YWxzOiBlcXVhbHMsXG4gICAgICAgICAgICAgICAgbWV0YTogbWV0YSxcbiAgICAgICAgICAgICAgICBzdHJpY3Q6IHN0cmljdCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoY2lyY3VsYXIpIHtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIGlzRXF1YWwoYSwgYikge1xuICAgICAgICAgICAgcmV0dXJuIGNvbXBhcmF0b3IoYSwgYiwge1xuICAgICAgICAgICAgICAgIGNhY2hlOiBuZXcgV2Vha01hcCgpLFxuICAgICAgICAgICAgICAgIGVxdWFsczogZXF1YWxzLFxuICAgICAgICAgICAgICAgIG1ldGE6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgICBzdHJpY3Q6IHN0cmljdCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICB2YXIgc3RhdGUgPSB7XG4gICAgICAgIGNhY2hlOiB1bmRlZmluZWQsXG4gICAgICAgIGVxdWFsczogZXF1YWxzLFxuICAgICAgICBtZXRhOiB1bmRlZmluZWQsXG4gICAgICAgIHN0cmljdDogc3RyaWN0LFxuICAgIH07XG4gICAgcmV0dXJuIGZ1bmN0aW9uIGlzRXF1YWwoYSwgYikge1xuICAgICAgICByZXR1cm4gY29tcGFyYXRvcihhLCBiLCBzdGF0ZSk7XG4gICAgfTtcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRoZSBpdGVtcyBwYXNzZWQgYXJlIGRlZXBseS1lcXVhbCBpbiB2YWx1ZS5cbiAqL1xudmFyIGRlZXBFcXVhbCA9IGNyZWF0ZUN1c3RvbUVxdWFsKCk7XG4vKipcbiAqIFdoZXRoZXIgdGhlIGl0ZW1zIHBhc3NlZCBhcmUgZGVlcGx5LWVxdWFsIGluIHZhbHVlIGJhc2VkIG9uIHN0cmljdCBjb21wYXJpc29uLlxuICovXG52YXIgc3RyaWN0RGVlcEVxdWFsID0gY3JlYXRlQ3VzdG9tRXF1YWwoeyBzdHJpY3Q6IHRydWUgfSk7XG4vKipcbiAqIFdoZXRoZXIgdGhlIGl0ZW1zIHBhc3NlZCBhcmUgZGVlcGx5LWVxdWFsIGluIHZhbHVlLCBpbmNsdWRpbmcgY2lyY3VsYXIgcmVmZXJlbmNlcy5cbiAqL1xudmFyIGNpcmN1bGFyRGVlcEVxdWFsID0gY3JlYXRlQ3VzdG9tRXF1YWwoeyBjaXJjdWxhcjogdHJ1ZSB9KTtcbi8qKlxuICogV2hldGhlciB0aGUgaXRlbXMgcGFzc2VkIGFyZSBkZWVwbHktZXF1YWwgaW4gdmFsdWUsIGluY2x1ZGluZyBjaXJjdWxhciByZWZlcmVuY2VzLFxuICogYmFzZWQgb24gc3RyaWN0IGNvbXBhcmlzb24uXG4gKi9cbnZhciBzdHJpY3RDaXJjdWxhckRlZXBFcXVhbCA9IGNyZWF0ZUN1c3RvbUVxdWFsKHtcbiAgICBjaXJjdWxhcjogdHJ1ZSxcbiAgICBzdHJpY3Q6IHRydWUsXG59KTtcbi8qKlxuICogV2hldGhlciB0aGUgaXRlbXMgcGFzc2VkIGFyZSBzaGFsbG93bHktZXF1YWwgaW4gdmFsdWUuXG4gKi9cbnZhciBzaGFsbG93RXF1YWwgPSBjcmVhdGVDdXN0b21FcXVhbCh7XG4gICAgY3JlYXRlSW50ZXJuYWxDb21wYXJhdG9yOiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYW1lVmFsdWVaZXJvRXF1YWw7IH0sXG59KTtcbi8qKlxuICogV2hldGhlciB0aGUgaXRlbXMgcGFzc2VkIGFyZSBzaGFsbG93bHktZXF1YWwgaW4gdmFsdWUgYmFzZWQgb24gc3RyaWN0IGNvbXBhcmlzb25cbiAqL1xudmFyIHN0cmljdFNoYWxsb3dFcXVhbCA9IGNyZWF0ZUN1c3RvbUVxdWFsKHtcbiAgICBzdHJpY3Q6IHRydWUsXG4gICAgY3JlYXRlSW50ZXJuYWxDb21wYXJhdG9yOiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYW1lVmFsdWVaZXJvRXF1YWw7IH0sXG59KTtcbi8qKlxuICogV2hldGhlciB0aGUgaXRlbXMgcGFzc2VkIGFyZSBzaGFsbG93bHktZXF1YWwgaW4gdmFsdWUsIGluY2x1ZGluZyBjaXJjdWxhciByZWZlcmVuY2VzLlxuICovXG52YXIgY2lyY3VsYXJTaGFsbG93RXF1YWwgPSBjcmVhdGVDdXN0b21FcXVhbCh7XG4gICAgY2lyY3VsYXI6IHRydWUsXG4gICAgY3JlYXRlSW50ZXJuYWxDb21wYXJhdG9yOiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYW1lVmFsdWVaZXJvRXF1YWw7IH0sXG59KTtcbi8qKlxuICogV2hldGhlciB0aGUgaXRlbXMgcGFzc2VkIGFyZSBzaGFsbG93bHktZXF1YWwgaW4gdmFsdWUsIGluY2x1ZGluZyBjaXJjdWxhciByZWZlcmVuY2VzLFxuICogYmFzZWQgb24gc3RyaWN0IGNvbXBhcmlzb24uXG4gKi9cbnZhciBzdHJpY3RDaXJjdWxhclNoYWxsb3dFcXVhbCA9IGNyZWF0ZUN1c3RvbUVxdWFsKHtcbiAgICBjaXJjdWxhcjogdHJ1ZSxcbiAgICBjcmVhdGVJbnRlcm5hbENvbXBhcmF0b3I6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHNhbWVWYWx1ZVplcm9FcXVhbDsgfSxcbiAgICBzdHJpY3Q6IHRydWUsXG59KTtcbi8qKlxuICogQ3JlYXRlIGEgY3VzdG9tIGVxdWFsaXR5IGNvbXBhcmlzb24gbWV0aG9kLlxuICpcbiAqIFRoaXMgY2FuIGJlIGRvbmUgdG8gY3JlYXRlIHZlcnkgdGFyZ2V0ZWQgY29tcGFyaXNvbnMgaW4gZXh0cmVtZSBob3QtcGF0aCBzY2VuYXJpb3NcbiAqIHdoZXJlIHRoZSBzdGFuZGFyZCBtZXRob2RzIGFyZSBub3QgcGVyZm9ybWFudCBlbm91Z2gsIGJ1dCBjYW4gYWxzbyBiZSB1c2VkIHRvIHByb3ZpZGVcbiAqIHN1cHBvcnQgZm9yIGxlZ2FjeSBlbnZpcm9ubWVudHMgdGhhdCBkbyBub3Qgc3VwcG9ydCBleHBlY3RlZCBmZWF0dXJlcyBsaWtlXG4gKiBgUmVnRXhwLnByb3RvdHlwZS5mbGFnc2Agb3V0IG9mIHRoZSBib3guXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZUN1c3RvbUVxdWFsKG9wdGlvbnMpIHtcbiAgICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7IG9wdGlvbnMgPSB7fTsgfVxuICAgIHZhciBfYSA9IG9wdGlvbnMuY2lyY3VsYXIsIGNpcmN1bGFyID0gX2EgPT09IHZvaWQgMCA/IGZhbHNlIDogX2EsIGNyZWF0ZUN1c3RvbUludGVybmFsQ29tcGFyYXRvciA9IG9wdGlvbnMuY3JlYXRlSW50ZXJuYWxDb21wYXJhdG9yLCBjcmVhdGVTdGF0ZSA9IG9wdGlvbnMuY3JlYXRlU3RhdGUsIF9iID0gb3B0aW9ucy5zdHJpY3QsIHN0cmljdCA9IF9iID09PSB2b2lkIDAgPyBmYWxzZSA6IF9iO1xuICAgIHZhciBjb25maWcgPSBjcmVhdGVFcXVhbGl0eUNvbXBhcmF0b3JDb25maWcob3B0aW9ucyk7XG4gICAgdmFyIGNvbXBhcmF0b3IgPSBjcmVhdGVFcXVhbGl0eUNvbXBhcmF0b3IoY29uZmlnKTtcbiAgICB2YXIgZXF1YWxzID0gY3JlYXRlQ3VzdG9tSW50ZXJuYWxDb21wYXJhdG9yXG4gICAgICAgID8gY3JlYXRlQ3VzdG9tSW50ZXJuYWxDb21wYXJhdG9yKGNvbXBhcmF0b3IpXG4gICAgICAgIDogY3JlYXRlSW50ZXJuYWxFcXVhbGl0eUNvbXBhcmF0b3IoY29tcGFyYXRvcik7XG4gICAgcmV0dXJuIGNyZWF0ZUlzRXF1YWwoeyBjaXJjdWxhcjogY2lyY3VsYXIsIGNvbXBhcmF0b3I6IGNvbXBhcmF0b3IsIGNyZWF0ZVN0YXRlOiBjcmVhdGVTdGF0ZSwgZXF1YWxzOiBlcXVhbHMsIHN0cmljdDogc3RyaWN0IH0pO1xufVxuXG5leHBvcnQgeyBjaXJjdWxhckRlZXBFcXVhbCwgY2lyY3VsYXJTaGFsbG93RXF1YWwsIGNyZWF0ZUN1c3RvbUVxdWFsLCBkZWVwRXF1YWwsIHNhbWVWYWx1ZVplcm9FcXVhbCwgc2hhbGxvd0VxdWFsLCBzdHJpY3RDaXJjdWxhckRlZXBFcXVhbCwgc3RyaWN0Q2lyY3VsYXJTaGFsbG93RXF1YWwsIHN0cmljdERlZXBFcXVhbCwgc3RyaWN0U2hhbGxvd0VxdWFsIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/fast-equals@5.2.2/node_modules/fast-equals/dist/esm/index.mjs\n");

/***/ })

};
;