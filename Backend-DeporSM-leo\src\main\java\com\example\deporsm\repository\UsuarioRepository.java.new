package com.example.deporsm.repository;

import com.example.deporsm.dto.AdministradorDTO;
import com.example.deporsm.dto.CoordinadorDTO;
import com.example.deporsm.dto.VecinoDTO;
import com.example.deporsm.dto.projections.VecinoDTOProjection;
import com.example.deporsm.model.Usuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UsuarioRepository extends JpaRepository<Usuario, Integer> {

    // Método para encontrar un usuario por su correo
    Optional<Usuario> findByEmail(String correo);

    // Count active users by role
    @Query("SELECT COUNT(u) FROM Usuario u WHERE u.activo = true")
    int countActiveUsers();

    @Query("SELECT COUNT(u) FROM Usuario u WHERE u.rol.id = :roleId AND u.activo = true")
    int countActiveUsersByRoleId(Integer roleId);

    // Get recent login activity
    @Query("SELECT u FROM Usuario u WHERE u.lastLogin IS NOT NULL ORDER BY u.lastLogin DESC")
    List<Usuario> findTop5ByOrderByLastLoginDesc();

    // Query para encontrar coordinadores con sus instalaciones asignadas
    @Query(value = """
    SELECT 
        u.id,
        CONCAT(u.nombre, ' ', u.apellidos) as nombre,
        u.email,
        u.telefono,
        GROUP_CONCAT(i.nombre ORDER BY i.nombre SEPARATOR ', ') as instalacionesAsignadas,
        u.activo as activo,
        DATE_FORMAT(u.last_login, '%Y-%m-%d %H:%i:%s') as ultimoAcceso
    FROM 
        deportes_sm.usuarios u
    LEFT JOIN 
        deportes_sm.coordinadores_instalaciones ci ON u.id = ci.usuario_id
    LEFT JOIN 
        deportes_sm.instalaciones i ON ci.instalacion_id = i.id
    WHERE 
        u.role_id = 3  -- Solo coordinadores
    GROUP BY 
        u.id, u.nombre, u.apellidos, u.email, u.telefono, u.activo, u.last_login, u.created_at
    """, nativeQuery = true)
    List<CoordinadorDTO> findAllCoordinadores();

    // Query para obtener administradores    
    @Query(value = """
        SELECT 
            u.id,
            CONCAT(u.nombre, ' ', u.apellidos) AS nombre,
            u.email,
            u.telefono,
            '' as instalacionesAsignadas,
            u.activo as activo 
        FROM 
            deportes_sm.usuarios u
        WHERE 
            u.role_id = 2
        GROUP BY 
            u.id, u.nombre, u.apellidos, u.email, u.telefono, u.activo
        ORDER BY 
            u.nombre, u.apellidos
        """, nativeQuery = true)
    List<AdministradorDTO> findAllAdministradores();

    // Get monthly growth percentage for all users - now using last 30 days
    @Query(value = """
    SELECT 
        CASE 
            WHEN prev_count = 0 THEN 100
            ELSE ROUND(((curr_count - prev_count) * 100.0 / prev_count), 0)
        END
    FROM (
        SELECT 
            (SELECT COUNT(*) 
             FROM usuarios 
             WHERE activo = true 
             AND created_at <= NOW()) as curr_count,
            (SELECT COUNT(*) 
             FROM usuarios 
             WHERE activo = true 
             AND created_at <= DATE_SUB(NOW(), INTERVAL 30 DAY)) as prev_count
    ) counts
    """, nativeQuery = true)
    int getMonthlyGrowthPercentage();

    // Get monthly growth percentage by role - now using last 30 days
    @Query(value = """
    SELECT 
        CASE 
            WHEN prev_count = 0 THEN 100
            ELSE ROUND(((curr_count - prev_count) * 100.0 / prev_count), 0)
        END
    FROM (
        SELECT 
            (SELECT COUNT(*) 
             FROM usuarios 
             WHERE activo = true 
             AND role_id = :roleId
             AND created_at <= NOW()) as curr_count,
            (SELECT COUNT(*) 
             FROM usuarios 
             WHERE activo = true 
             AND role_id = :roleId
             AND created_at <= DATE_SUB(NOW(), INTERVAL 30 DAY)) as prev_count
    ) counts
    """, nativeQuery = true)
    int getMonthlyGrowthPercentageByRole(Integer roleId);

    @Query(value = """
    SELECT 
        u.id as id,
        CONCAT(u.nombre, ' ', u.apellidos) as nombre,
        u.email as email,
        u.telefono as telefono,
        u.direccion as direccion,
        u.dni as dni,
        u.activo as activo,
        DATE_FORMAT(u.last_login, '%Y-%m-%d %H:%i:%s') as lastLogin,
        COUNT(r.id) as reservas
    FROM 
        deportes_sm.usuarios u
    LEFT JOIN 
        deportes_sm.reservas r ON u.id = r.usuario_id
    WHERE 
        u.role_id = 4
    GROUP BY 
        u.id, u.nombre, u.apellidos, u.email, u.telefono, u.direccion, u.dni, u.activo, u.last_login
    """, nativeQuery = true)
    List<VecinoDTOProjection> findAllVecinos();
}
