# Nombre de la aplicación
spring.application.name=deporsm

# Conexión a base de datos local
spring.datasource.url=***************************************
spring.datasource.username=root
spring.datasource.password=root

# Configuración JPA y Hibernate
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true
spring.datasource.hikari.connection-timeout=20000

# Logging para depuración con Spring Security
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web.FilterChainProxy=DEBUG

# Configuración de sesiones para desarrollo
server.servlet.session.timeout=24h
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.same-site=lax
server.servlet.session.cookie.path=/
server.servlet.session.cookie.name=JSESSIONID
server.servlet.session.cookie.max-age=86400

# Usar la gestión de sesiones integrada de Tomcat
spring.session.store-type=none
server.servlet.session.tracking-modes=cookie

# Configuración CORS para desarrollo local
app.cors.allowed-origins=http://localhost:3000

# URL del frontend para enlaces en correos electrónicos
app.frontend-url=http://localhost:3000

# Configuración de correo electrónico usando Mailtrap para pruebas
spring.mail.host=sandbox.smtp.mailtrap.io
spring.mail.port=2525
spring.mail.username=7e0a5a3e3e3e3e
spring.mail.password=7e0a5a3e3e3e3e
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.properties.mail.smtp.debug=true
spring.mail.properties.mail.transport.protocol=smtp
spring.mail.sender-name=DeporSM - Municipalidad de San Miguel

# Habilitar logging para Spring Mail
logging.level.org.springframework.mail=DEBUG

# Configuración de contacto
contacto.email.destinatario=<EMAIL>
