globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/chatbot.tsx":{"*":{"id":"(ssr)/./components/chatbot.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(ssr)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout-client-wrapper.tsx":{"*":{"id":"(ssr)/./components/layout-client-wrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/metadata-generator.tsx":{"*":{"id":"(ssr)/./components/metadata-generator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/AuthContext.tsx":{"*":{"id":"(ssr)/./context/AuthContext.tsx?00e6","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/ConfiguracionContext.tsx":{"*":{"id":"(ssr)/./context/ConfiguracionContext.tsx?fca1","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/NotificationContext.tsx":{"*":{"id":"(ssr)/./context/NotificationContext.tsx?08c2","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js?efae","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\components\\chatbot.tsx":{"id":"(app-pages-browser)/./components/chatbot.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\components\\footer.tsx":{"id":"(app-pages-browser)/./components/footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\components\\layout-client-wrapper.tsx":{"id":"(app-pages-browser)/./components/layout-client-wrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\components\\metadata-generator.tsx":{"id":"(app-pages-browser)/./components/metadata-generator.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\context\\AuthContext.tsx":{"id":"(app-pages-browser)/./context/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\context\\ConfiguracionContext.tsx":{"id":"(app-pages-browser)/./context/ConfiguracionContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\context\\NotificationContext.tsx":{"id":"(app-pages-browser)/./context/NotificationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\":[],"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\app\\page":[],"C:\\Users\\<USER>\\GTICS\\UE\\Frontend-DeporSM-leo\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/chatbot.tsx":{"*":{"id":"(rsc)/./components/chatbot.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(rsc)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout-client-wrapper.tsx":{"*":{"id":"(rsc)/./components/layout-client-wrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/metadata-generator.tsx":{"*":{"id":"(rsc)/./components/metadata-generator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/AuthContext.tsx":{"*":{"id":"(rsc)/./context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/ConfiguracionContext.tsx":{"*":{"id":"(rsc)/./context/ConfiguracionContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/NotificationContext.tsx":{"*":{"id":"(rsc)/./context/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/error-boundary.js?efae","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../../AppData/Roaming/npm/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}}}