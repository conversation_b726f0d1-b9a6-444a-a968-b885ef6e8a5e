name: 'Build and Deploy to Cloud Run'

on:
  push:
    branches:
      - "main"

env:
  PROJECT_ID: 'logical-pathway-459914-i3'
  REGION: 'us-central1'
  SERVICE: 'deporsm-apiwith'

jobs:
  deploy:
    runs-on: 'ubuntu-latest'

    steps:
      - name: 'Checkout source'
        uses: actions/checkout@v4

      - name: 'Authenticate to Google Cloud'
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_SA_KEY }}'

      - name: 'Configure Docker for Artifact Registry'
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: 'Build and Push Container'
        run: |
          IMAGE="${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/artifactdeporsm/${{ env.SERVICE }}:${{ github.sha }}"
          docker build -t $IMAGE .
          docker push $IMAGE
          echo "IMAGE=$IMAGE" >> $GITHUB_ENV

      - name: 'Deploy to Cloud Run'
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: '${{ env.SERVICE }}'
          region: '${{ env.REGION }}'
          image: '${{ env.IMAGE }}'
          env_vars: |
            SPRING_DATASOURCE_URL=*****************************************************************************************************************************************************************
            SPRING_DATASOURCE_USERNAME=user
            SPRING_DATASOURCE_PASSWORD=gatoswapos123-
