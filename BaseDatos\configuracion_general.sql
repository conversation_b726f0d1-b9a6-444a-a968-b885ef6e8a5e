-- Archivo SQL para crear la tabla de configuración general
-- Este script debe ser ejecutado en la base de datos

-- Crear la tabla si no existe
CREATE TABLE IF NOT EXISTS configuracion_general (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre_sitio VARCHAR(255) NOT NULL,
    descripcion_sitio TEXT,
    telefono_contacto VARCHAR(50),
    email_contacto VARCHAR(255),
    max_reservas_por_usuario INT DEFAULT 3,
    limite_tiempo_cancelacion INT DEFAULT 48,
    modo_mantenimiento BOOLEAN DEFAULT FALSE,
    registro_habilitado BOOLEAN DEFAULT TRUE,
    reservas_habilitadas BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar un registro inicial si la tabla está vacía
INSERT INTO configuracion_general (id, nombre_sitio, descripcion_sitio, telefono_contacto, email_contacto)
SELECT 1, 'DeporSM - Sistema de Reservas Deportivas', 'Sistema de reserva de canchas y servicios deportivos para la Municipalidad de San Miguel.', '987-654-321', '<EMAIL>'
WHERE NOT EXISTS (SELECT 1 FROM configuracion_general WHERE id = 1);
