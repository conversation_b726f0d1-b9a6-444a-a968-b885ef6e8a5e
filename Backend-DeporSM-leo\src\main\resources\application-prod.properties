# Nombre de la aplicación
spring.application.name=deporsm

# Conexión a Cloud SQL usando SocketFactory de Google con configuración de zona horaria
spring.datasource.url=******************************************************************************************************************************************************************************************************************
spring.datasource.username=user
spring.datasource.password=gatoswapos123-

# Configuración JPA y Hibernate
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true
spring.datasource.hikari.connection-timeout=20000

# Configuración de zona horaria para Perú (GMT-5)
spring.jpa.properties.hibernate.jdbc.time_zone=America/Lima

# Logging para depuración con Spring Security
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web.FilterChainProxy=DEBUG

# Configuración de sesiones para producción
server.servlet.session.timeout=24h
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.same-site=none
server.servlet.session.cookie.path=/
server.servlet.session.cookie.name=JSESSIONID
server.servlet.session.cookie.max-age=86400

# Usar la gestión de sesiones integrada de Tomcat
spring.session.store-type=none
server.servlet.session.tracking-modes=cookie

# Configuración CORS para producción
app.cors.allowed-origins=https://deporsm-apiwith-1035693188565.us-central1.run.app,https://frontend-depor-sm-leo.vercel.app,http://localhost:3000,https://frontend-depor-sm-leo-leonardo-pucps-projects.vercel.app

# URL del frontend para enlaces en correos electrónicos
app.frontend-url=https://frontend-depor-sm-leo-leonardo-pucps-projects.vercel.app

# Configuración de correo electrónico
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=SPRING_MAIL_USERNAME
spring.mail.password=SPRING_MAIL_PASSWORD
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.properties.mail.smtp.debug=true
spring.mail.properties.mail.transport.protocol=smtp
spring.mail.sender-name=DeporSM - Municipalidad de San Miguel

# Habilitar logging para Spring Mail
logging.level.org.springframework.mail=DEBUG

# Configuración de contacto
contacto.email.destinatario=<EMAIL>
