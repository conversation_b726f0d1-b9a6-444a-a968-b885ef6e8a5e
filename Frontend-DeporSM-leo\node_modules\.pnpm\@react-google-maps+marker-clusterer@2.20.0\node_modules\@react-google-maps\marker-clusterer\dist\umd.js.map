{"version": 3, "file": "umd.js", "sources": ["../src/ClusterIcon.tsx", "../src/Cluster.tsx", "../src/Clusterer.tsx"], "sourcesContent": ["/* global google */\n/* eslint-disable filenames/match-regex */\nimport type { Cluster } from './Cluster'\n\nimport type { ClusterIconStyle, ClusterIconInfo } from './types'\n\nexport class ClusterIcon {\n  cluster: Cluster\n  className: string\n  clusterClassName: string\n  styles: ClusterIconStyle[]\n  center: google.maps.LatLng | undefined\n  div: HTMLDivElement | null\n  sums: ClusterIconInfo | null\n  visible: boolean\n  url: string\n  height: number\n  width: number\n  anchorText: [number, number]\n  anchorIcon: [number, number]\n  textColor: string\n  textSize: number\n  textDecoration: string\n  fontWeight: string\n  fontStyle: string\n  fontFamily: string\n  backgroundPosition: string\n  cMouseDownInCluster: boolean | null\n  cDraggingMapByCluster: boolean | null\n  timeOut: number | null\n\n  boundsChangedListener: google.maps.MapsEventListener | null\n\n  constructor(cluster: Cluster, styles: ClusterIconStyle[]) {\n    cluster.getClusterer().extend(ClusterIcon, google.maps.OverlayView)\n\n    this.cluster = cluster\n\n    this.clusterClassName = this.cluster.getClusterer().getClusterClass()\n\n    this.className = this.clusterClassName\n\n    this.styles = styles\n\n    this.center = undefined\n\n    this.div = null\n\n    this.sums = null\n\n    this.visible = false\n\n    this.boundsChangedListener = null\n\n    this.url = ''\n\n    this.height = 0\n    this.width = 0\n\n    this.anchorText = [0, 0]\n    this.anchorIcon = [0, 0]\n\n    this.textColor = 'black'\n    this.textSize = 11\n    this.textDecoration = 'none'\n    this.fontWeight = 'bold'\n    this.fontStyle = 'normal'\n    this.fontFamily = 'Arial,sans-serif'\n\n    this.backgroundPosition = '0 0'\n\n    this.cMouseDownInCluster = null\n    this.cDraggingMapByCluster = null\n    this.timeOut = null;\n\n    (this as unknown as google.maps.OverlayView).setMap(cluster.getMap()) // Note: this causes onAdd to be called\n\n    this.onBoundsChanged = this.onBoundsChanged.bind(this)\n    this.onMouseDown = this.onMouseDown.bind(this)\n    this.onClick = this.onClick.bind(this)\n    this.onMouseOver = this.onMouseOver.bind(this)\n    this.onMouseOut = this.onMouseOut.bind(this)\n    this.onAdd = this.onAdd.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.draw = this.draw.bind(this)\n    this.hide = this.hide.bind(this)\n    this.show = this.show.bind(this)\n    this.useStyle = this.useStyle.bind(this)\n    this.setCenter = this.setCenter.bind(this)\n    this.getPosFromLatLng = this.getPosFromLatLng.bind(this)\n  }\n\n  onBoundsChanged() {\n    this.cDraggingMapByCluster = this.cMouseDownInCluster\n  }\n\n  onMouseDown() {\n    this.cMouseDownInCluster = true\n\n    this.cDraggingMapByCluster = false\n  }\n\n  onClick(event: Event) {\n    this.cMouseDownInCluster = false\n\n    if (!this.cDraggingMapByCluster) {\n      const markerClusterer = this.cluster.getClusterer()\n\n      /**\n       * This event is fired when a cluster marker is clicked.\n       * @name MarkerClusterer#click\n       * @param {Cluster} c The cluster that was clicked.\n       * @event\n       */\n      google.maps.event.trigger(markerClusterer, 'click', this.cluster)\n      google.maps.event.trigger(markerClusterer, 'clusterclick', this.cluster) // deprecated name\n\n      // The default click handler follows. Disable it by setting\n      // the zoomOnClick property to false.\n      if (markerClusterer.getZoomOnClick()) {\n        // Zoom into the cluster.\n        const maxZoom = markerClusterer.getMaxZoom()\n\n        const bounds = this.cluster.getBounds()\n\n        const map = (markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n        if (map !== null && 'fitBounds' in map) {\n          map.fitBounds(bounds)\n        }\n\n\n        // There is a fix for Issue 170 here:\n        this.timeOut = window.setTimeout(() => {\n          const map = (markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n          if (map !== null) {\n            if ('fitBounds' in map) {\n              map.fitBounds(bounds)\n            }\n\n            const zoom = map.getZoom() || 0\n\n            // Don't zoom beyond the max zoom level\n            if (\n              maxZoom !== null &&\n              zoom > maxZoom\n            ) {\n              map.setZoom(maxZoom + 1)\n            }\n          }\n        }, 100)\n      }\n\n      // Prevent event propagation to the map:\n      event.cancelBubble = true\n\n      if (event.stopPropagation) {\n        event.stopPropagation()\n      }\n    }\n  }\n\n  onMouseOver() {\n    /**\n     * This event is fired when the mouse moves over a cluster marker.\n     * @name MarkerClusterer#mouseover\n     * @param {Cluster} c The cluster that the mouse moved over.\n     * @event\n     */\n    google.maps.event.trigger(\n      this.cluster.getClusterer(),\n      'mouseover',\n      this.cluster\n    )\n  }\n\n  onMouseOut() {\n    /**\n     * This event is fired when the mouse moves out of a cluster marker.\n     * @name MarkerClusterer#mouseout\n     * @param {Cluster} c The cluster that the mouse moved out of.\n     * @event\n     */\n    google.maps.event.trigger(\n      this.cluster.getClusterer(),\n      'mouseout',\n      this.cluster\n    )\n  }\n\n  onAdd() {\n    this.div = document.createElement('div')\n\n    this.div.className = this.className\n\n    if (this.visible) {\n      this.show()\n    }\n\n    ;(this as unknown as google.maps.OverlayView).getPanes()?.overlayMouseTarget.appendChild(this.div)\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    if (map !== null) {\n      // Fix for Issue 157\n      this.boundsChangedListener = google.maps.event.addListener(\n        map,\n        'bounds_changed',\n        this.onBoundsChanged\n      )\n\n      this.div.addEventListener('mousedown', this.onMouseDown)\n\n      this.div.addEventListener('click', this.onClick)\n\n      this.div.addEventListener('mouseover', this.onMouseOver)\n\n      this.div.addEventListener('mouseout', this.onMouseOut)\n    }\n  }\n\n  onRemove() {\n    if (this.div && this.div.parentNode) {\n      this.hide()\n\n      if (this.boundsChangedListener !== null) {\n        google.maps.event.removeListener(this.boundsChangedListener)\n      }\n\n      this.div.removeEventListener('mousedown', this.onMouseDown)\n\n      this.div.removeEventListener('click', this.onClick)\n\n      this.div.removeEventListener('mouseover', this.onMouseOver)\n\n      this.div.removeEventListener('mouseout', this.onMouseOut)\n\n      this.div.parentNode.removeChild(this.div)\n\n      if (this.timeOut !== null) {\n        window.clearTimeout(this.timeOut)\n\n        this.timeOut = null\n      }\n\n      this.div = null\n    }\n  }\n\n  draw() {\n    if (this.visible && this.div !== null && this.center) {\n      const pos = this.getPosFromLatLng(this.center)\n\n      this.div.style.top = pos !== null ? `${pos.y}px` : '0'\n      this.div.style.left = pos !== null ? `${pos.x}px` : '0'\n    }\n  }\n\n  hide() {\n    if (this.div) {\n      this.div.style.display = 'none'\n    }\n\n    this.visible = false\n  }\n\n  show() {\n    if (this.div && this.center) {\n      const divTitle = this.sums === null ||\n      typeof this.sums.title === 'undefined' ||\n      this.sums.title === '' ? this.cluster.getClusterer().getTitle() :  this.sums.title\n\n      // NOTE: values must be specified in px units\n      const bp = this.backgroundPosition.split(' ')\n\n      const spriteH = parseInt(bp[0]?.replace(/^\\s+|\\s+$/g, '') || '0', 10)\n      const spriteV = parseInt(bp[1]?.replace(/^\\s+|\\s+$/g, '') || '0', 10)\n\n      const pos = this.getPosFromLatLng(this.center)\n\n      this.div.className = this.className\n      this.div .setAttribute('style', `cursor: pointer; position: absolute; top: ${pos !== null ? `${pos.y}px` : '0'}; left: ${pos !== null ? `${pos.x}px` : '0'}; width: ${this.width}px; height: ${this.height}px; `)\n\n      const img = document.createElement('img')\n\n      img.alt = divTitle\n      img.src = this.url\n      img.width = this.width\n      img.height = this.height\n      img.setAttribute('style', `position: absolute; top: ${spriteV}px; left: ${spriteH}px`)\n\n      if (!this.cluster.getClusterer().enableRetinaIcons) {\n        img.style.clip = `rect(-${spriteV}px, -${spriteH + this.width}px, -${\n          spriteV + this.height\n        }, -${spriteH})`\n      }\n\n      const textElm = document.createElement('div')\n\n      textElm .setAttribute('style', `position: absolute; top: ${this.anchorText[0]}px; left: ${this.anchorText[1]}px; color: ${this.textColor}; font-size: ${this.textSize}px; font-family: ${this.fontFamily}; font-weight: ${this.fontWeight}; fontStyle: ${this.fontStyle}; text-decoration: ${this.textDecoration}; text-align: center; width: ${this.width}px; line-height: ${this.height}px`)\n\n      if (this.sums?.text) textElm.innerText = `${this.sums?.text}`\n      if (this.sums?.html) textElm.innerHTML = `${this.sums?.html}`\n\n      this.div.innerHTML = ''\n\n      this.div.appendChild(img)\n      this.div.appendChild(textElm)\n\n      this.div.title = divTitle\n\n      this.div.style.display = ''\n    }\n\n    this.visible = true\n  }\n\n  useStyle(sums: ClusterIconInfo) {\n    this.sums = sums\n\n    const styles = this.cluster.getClusterer().getStyles()\n\n    const style =\n      styles[Math.min(styles.length - 1, Math.max(0, sums.index - 1))]\n\n    if (style) {\n      this.url = style.url\n      this.height = style.height\n      this.width = style.width\n\n      if (style.className) {\n        this.className = `${this.clusterClassName} ${style.className}`\n      }\n\n      this.anchorText = style.anchorText || [0, 0]\n      this.anchorIcon = style.anchorIcon || [this.height / 2, this.width / 2]\n\n      this.textColor = style.textColor || 'black'\n\n      this.textSize = style.textSize || 11\n\n      this.textDecoration = style.textDecoration || 'none'\n\n      this.fontWeight = style.fontWeight || 'bold'\n\n      this.fontStyle = style.fontStyle || 'normal'\n\n      this.fontFamily = style.fontFamily || 'Arial,sans-serif'\n\n      this.backgroundPosition = style.backgroundPosition || '0 0'\n    }\n  }\n\n  setCenter(center: google.maps.LatLng) {\n    this.center = center\n  }\n\n  getPosFromLatLng(latlng: google.maps.LatLng): google.maps.Point | null {\n    const pos = (this as unknown as google.maps.OverlayView).getProjection().fromLatLngToDivPixel(latlng)\n\n    if (pos !== null) {\n      pos.x -= this.anchorIcon[1]\n\n      pos.y -= this.anchorIcon[0]\n    }\n\n    return pos\n  }\n}\n", "/* global google */\n\nimport type { <PERSON>luster<PERSON> } from './Clusterer'\n\nimport { ClusterIcon } from './ClusterIcon'\n\nimport type { MarkerExtended } from './types'\n\nexport class Cluster {\n  markerClusterer: Clusterer\n  map: google.maps.Map | google.maps.StreetViewPanorama | null\n  gridSize: number\n  minClusterSize: number\n  averageCenter: boolean\n  markers: MarkerExtended[]\n  center: google.maps.LatLng | undefined\n  bounds: google.maps.LatLngBounds | null\n  clusterIcon: ClusterIcon\n\n  constructor(markerClusterer: Clusterer) {\n    this.markerClusterer = markerClusterer\n\n    this.map = (this.markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n    this.gridSize = this.markerClusterer.getGridSize()\n\n    this.minClusterSize = this.markerClusterer.getMinimumClusterSize()\n\n    this.averageCenter = this.markerClusterer.getAverageCenter()\n\n    this.markers = []\n\n    this.center = undefined\n\n    this.bounds = null\n\n    this.clusterIcon = new ClusterIcon(this, this.markerClusterer.getStyles())\n\n    this.getSize = this.getSize.bind(this)\n    this.getMarkers = this.getMarkers.bind(this)\n    this.getCenter = this.getCenter.bind(this)\n    this.getMap = this.getMap.bind(this)\n    this.getClusterer = this.getClusterer.bind(this)\n    this.getBounds = this.getBounds.bind(this)\n    this.remove = this.remove.bind(this)\n    this.addMarker = this.addMarker.bind(this)\n    this.isMarkerInClusterBounds = this.isMarkerInClusterBounds.bind(this)\n    this.calculateBounds = this.calculateBounds.bind(this)\n    this.updateIcon = this.updateIcon.bind(this)\n    this.isMarkerAlreadyAdded = this.isMarkerAlreadyAdded.bind(this)\n  }\n\n  getSize(): number {\n    return this.markers.length\n  }\n\n  getMarkers(): MarkerExtended[] {\n    return this.markers\n  }\n\n  getCenter(): google.maps.LatLng | undefined {\n    return this.center\n  }\n\n  getMap(): google.maps.Map | google.maps.StreetViewPanorama | null {\n    return this.map\n  }\n\n  getClusterer(): Clusterer {\n    return this.markerClusterer\n  }\n\n  getBounds(): google.maps.LatLngBounds {\n    const bounds = new google.maps.LatLngBounds(this.center, this.center)\n\n    const markers = this.getMarkers()\n\n    for (const marker of markers) {\n      const position = marker.getPosition()\n\n      if (position) {\n        bounds.extend(position)\n      }\n    }\n\n    return bounds\n  }\n\n  remove() {\n    (this.clusterIcon as unknown as google.maps.OverlayView).setMap(null)\n\n    this.markers = []\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    delete this.markers\n  }\n\n  addMarker(marker: MarkerExtended): boolean {\n    if (this.isMarkerAlreadyAdded(marker)) {\n      return false\n    }\n\n    if (!this.center) {\n      const position = marker.getPosition()\n\n      if (position) {\n        this.center = position\n\n        this.calculateBounds()\n      }\n    } else {\n      if (this.averageCenter) {\n        const position = marker.getPosition()\n\n        if (position) {\n          const length = this.markers.length + 1\n\n          this.center = new google.maps.LatLng(\n            (this.center.lat() * (length - 1) + position.lat()) / length,\n            (this.center.lng() * (length - 1) + position.lng()) / length\n          )\n\n          this.calculateBounds()\n        }\n      }\n    }\n\n    marker.isAdded = true\n\n    this.markers.push(marker)\n\n    const mCount = this.markers.length\n\n    const maxZoom = this.markerClusterer.getMaxZoom()\n\n    const zoom = this.map?.getZoom()\n\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      // Zoomed in past max zoom, so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map)\n      }\n    } else if (mCount < this.minClusterSize) {\n      // Min cluster size not reached so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map)\n      }\n    } else if (mCount === this.minClusterSize) {\n      // Hide the markers that were showing.\n      for (const markerElement of this.markers) {\n        markerElement.setMap(null)\n      }\n    } else {\n      marker.setMap(null)\n    }\n\n    return true\n  }\n\n  isMarkerInClusterBounds(marker: MarkerExtended): boolean {\n    if (this.bounds !== null) {\n      const position = marker.getPosition()\n\n      if (position) {\n        return this.bounds.contains(position)\n      }\n    }\n\n    return false\n  }\n\n  calculateBounds() {\n    this.bounds = this.markerClusterer.getExtendedBounds(\n      new google.maps.LatLngBounds(this.center, this.center)\n    )\n  }\n\n  updateIcon() {\n    const mCount = this.markers.length\n\n    const maxZoom = this.markerClusterer.getMaxZoom()\n\n    const zoom = this.map?.getZoom()\n\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      this.clusterIcon.hide()\n\n      return\n    }\n\n    if (mCount < this.minClusterSize) {\n      // Min cluster size not yet reached.\n      this.clusterIcon.hide()\n\n      return\n    }\n\n    if (this.center) {\n      this.clusterIcon.setCenter(this.center)\n    }\n\n    this.clusterIcon.useStyle(\n      this.markerClusterer.getCalculator()(this.markers, this.markerClusterer.getStyles().length)\n    )\n\n    this.clusterIcon.show()\n  }\n\n  isMarkerAlreadyAdded(marker: MarkerExtended): boolean {\n    if (this.markers.includes) {\n      return this.markers.includes(marker)\n    }\n\n    for (let i = 0; i < this.markers.length; i++) {\n      if (marker === this.markers[i]) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n", "/* global google */\n/* eslint-disable filenames/match-regex */\nimport { Cluster } from './Cluster'\nimport type { ClusterIcon } from './ClusterIcon'\n\nimport type {\n  MarkerExtended,\n  ClustererOptions,\n  ClusterIconStyle,\n  TCalculator,\n  ClusterIconInfo,\n} from './types'\n\n/**\n * Supports up to 9007199254740991 (Number.MAX_SAFE_INTEGER) markers\n * which is not a problem as max array length is 4294967296 (2**32)\n */\nfunction CALCULATOR(\n  markers: MarkerExtended[],\n  numStyles: number\n): ClusterIconInfo {\n  const count = markers.length\n\n  const numberOfDigits = count.toString().length\n\n  const index = Math.min(numberOfDigits, numStyles)\n\n  return {\n    text: count.toString(),\n    index,\n    title: '',\n  }\n}\n\nconst BATCH_SIZE = 2000\n\nconst BATCH_SIZE_IE = 500\n\nconst IMAGE_PATH =\n  'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'\n\nconst IMAGE_EXTENSION = 'png'\n\nconst IMAGE_SIZES = [53, 56, 66, 78, 90]\n\nconst CLUSTERER_CLASS = 'cluster'\n\nexport class Clusterer implements google.maps.OverlayView {\n  markers: MarkerExtended[]\n  clusters: Cluster[]\n  listeners: google.maps.MapsEventListener[]\n  activeMap: google.maps.Map | google.maps.StreetViewPanorama | null\n  ready: boolean\n  gridSize: number\n  minClusterSize: number\n  maxZoom: number | null\n  styles: ClusterIconStyle[]\n  title: string\n  zoomOnClick: boolean\n  averageCenter: boolean\n  ignoreHidden: boolean\n  enableRetinaIcons: boolean\n  imagePath: string\n  imageExtension: string\n  imageSizes: number[]\n  calculator: TCalculator\n  batchSize: number\n  batchSizeIE: number\n  clusterClass: string\n  timerRefStatic: number | null\n\n  constructor(\n    map: google.maps.Map,\n    optMarkers: MarkerExtended[] = [],\n    optOptions: ClustererOptions = {}\n  ) {\n    this.getMinimumClusterSize = this.getMinimumClusterSize.bind(this)\n    this.setMinimumClusterSize = this.setMinimumClusterSize.bind(this)\n    this.getEnableRetinaIcons = this.getEnableRetinaIcons.bind(this)\n    this.setEnableRetinaIcons = this.setEnableRetinaIcons.bind(this)\n    this.addToClosestCluster = this.addToClosestCluster.bind(this)\n    this.getImageExtension = this.getImageExtension.bind(this)\n    this.setImageExtension = this.setImageExtension.bind(this)\n    this.getExtendedBounds = this.getExtendedBounds.bind(this)\n    this.getAverageCenter = this.getAverageCenter.bind(this)\n    this.setAverageCenter = this.setAverageCenter.bind(this)\n    this.getTotalClusters = this.getTotalClusters.bind(this)\n    this.fitMapToMarkers = this.fitMapToMarkers.bind(this)\n    this.getIgnoreHidden = this.getIgnoreHidden.bind(this)\n    this.setIgnoreHidden = this.setIgnoreHidden.bind(this)\n    this.getClusterClass = this.getClusterClass.bind(this)\n    this.setClusterClass = this.setClusterClass.bind(this)\n    this.getTotalMarkers = this.getTotalMarkers.bind(this)\n    this.getZoomOnClick = this.getZoomOnClick.bind(this)\n    this.setZoomOnClick = this.setZoomOnClick.bind(this)\n    this.getBatchSizeIE = this.getBatchSizeIE.bind(this)\n    this.setBatchSizeIE = this.setBatchSizeIE.bind(this)\n    this.createClusters = this.createClusters.bind(this)\n    this.onZoomChanged = this.onZoomChanged.bind(this)\n    this.getImageSizes = this.getImageSizes.bind(this)\n    this.setImageSizes = this.setImageSizes.bind(this)\n    this.getCalculator = this.getCalculator.bind(this)\n    this.setCalculator = this.setCalculator.bind(this)\n    this.removeMarkers = this.removeMarkers.bind(this)\n    this.resetViewport = this.resetViewport.bind(this)\n    this.getImagePath = this.getImagePath.bind(this)\n    this.setImagePath = this.setImagePath.bind(this)\n    this.pushMarkerTo = this.pushMarkerTo.bind(this)\n    this.removeMarker = this.removeMarker.bind(this)\n    this.clearMarkers = this.clearMarkers.bind(this)\n    this.setupStyles = this.setupStyles.bind(this)\n    this.getGridSize = this.getGridSize.bind(this)\n    this.setGridSize = this.setGridSize.bind(this)\n    this.getClusters = this.getClusters.bind(this)\n    this.getMaxZoom = this.getMaxZoom.bind(this)\n    this.setMaxZoom = this.setMaxZoom.bind(this)\n    this.getMarkers = this.getMarkers.bind(this)\n    this.addMarkers = this.addMarkers.bind(this)\n    this.getStyles = this.getStyles.bind(this)\n    this.setStyles = this.setStyles.bind(this)\n    this.addMarker = this.addMarker.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.getTitle = this.getTitle.bind(this)\n    this.setTitle = this.setTitle.bind(this)\n    this.repaint = this.repaint.bind(this)\n    this.onIdle = this.onIdle.bind(this)\n    this.redraw = this.redraw.bind(this)\n    this.onAdd = this.onAdd.bind(this)\n    this.draw = this.draw.bind(this)\n\n    this.extend = this.extend.bind(this)\n    this.extend(Clusterer, google.maps.OverlayView)\n\n    this.markers = []\n    this.clusters = []\n    this.listeners = []\n    this.activeMap = null\n    this.ready = false\n    this.gridSize = optOptions.gridSize || 60\n    this.minClusterSize = optOptions.minimumClusterSize || 2\n    this.maxZoom = optOptions.maxZoom || null\n    this.styles = optOptions.styles || []\n\n    this.title = optOptions.title || ''\n\n    this.zoomOnClick = true\n\n    if (optOptions.zoomOnClick !== undefined) {\n      this.zoomOnClick = optOptions.zoomOnClick\n    }\n\n    this.averageCenter = false\n\n    if (optOptions.averageCenter !== undefined) {\n      this.averageCenter = optOptions.averageCenter\n    }\n\n    this.ignoreHidden = false\n\n    if (optOptions.ignoreHidden !== undefined) {\n      this.ignoreHidden = optOptions.ignoreHidden\n    }\n\n    this.enableRetinaIcons = false\n\n    if (optOptions.enableRetinaIcons !== undefined) {\n      this.enableRetinaIcons = optOptions.enableRetinaIcons\n    }\n    this.imagePath = optOptions.imagePath || IMAGE_PATH\n\n    this.imageExtension = optOptions.imageExtension || IMAGE_EXTENSION\n\n    this.imageSizes = optOptions.imageSizes || IMAGE_SIZES\n\n    this.calculator = optOptions.calculator || CALCULATOR\n\n    this.batchSize = optOptions.batchSize || BATCH_SIZE\n\n    this.batchSizeIE = optOptions.batchSizeIE || BATCH_SIZE_IE\n\n    this.clusterClass = optOptions.clusterClass || CLUSTERER_CLASS\n\n    if (navigator.userAgent.toLowerCase().indexOf('msie') !== -1) {\n      // Try to avoid IE timeout when processing a huge number of markers:\n      this.batchSize = this.batchSizeIE\n    }\n\n    this.timerRefStatic = null\n\n    this.setupStyles()\n\n    this.addMarkers(optMarkers, true);\n\n    (this as unknown as google.maps.OverlayView).setMap(map) // Note: this causes onAdd to be called\n  }\n\n  onZoomChanged(): void {\n    this.resetViewport(false)\n\n    // Workaround for this Google bug: when map is at level 0 and \"-\" of\n    // zoom slider is clicked, a \"zoom_changed\" event is fired even though\n    // the map doesn't zoom out any further. In this situation, no \"idle\"\n    // event is triggered so the cluster markers that have been removed\n    // do not get redrawn. Same goes for a zoom in at maxZoom.\n    if (\n      (this as unknown as google.maps.OverlayView).getMap()?.getZoom() === ((this as unknown as google.maps.OverlayView).get('minZoom') || 0) ||\n      (this as unknown as google.maps.OverlayView).getMap()?.getZoom() === (this as unknown as google.maps.OverlayView).get('maxZoom')\n    ) {\n      google.maps.event.trigger(this, 'idle')\n    }\n  }\n\n  onIdle(): void {\n    this.redraw()\n  }\n\n  onAdd(): void {\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    this.activeMap = map\n\n    this.ready = true\n\n    this.repaint()\n\n    if (map !== null) {\n      // Add the map event listeners\n      this.listeners = [\n        google.maps.event.addListener(\n          map,\n          'zoom_changed',\n          this.onZoomChanged\n        ),\n        google.maps.event.addListener(\n          map,\n          'idle',\n          this.onIdle\n        ),\n      ]\n    }\n  }\n\n  onRemove(): void {\n    // Put all the managed markers back on the map:\n    for (const marker of this.markers) {\n      if (marker.getMap() !== this.activeMap) {\n        marker.setMap(this.activeMap)\n      }\n    }\n\n    // Remove all clusters:\n    for (const cluster of this.clusters) {\n      cluster.remove()\n    }\n\n    this.clusters = []\n\n    // Remove map event listeners:\n    for (const listener of this.listeners) {\n      google.maps.event.removeListener(listener)\n    }\n\n    this.listeners = []\n\n    this.activeMap = null\n\n    this.ready = false\n  }\n\n  draw(): void { return }\n\n  getMap(): null { return null }\n\n  getPanes(): null { return null }\n\n  getProjection()  {\n    return {\n      fromContainerPixelToLatLng(): null { return null },\n      fromDivPixelToLatLng(): null { return null},\n      fromLatLngToContainerPixel(): null { return null},\n      fromLatLngToDivPixel(): null { return null},\n      getVisibleRegion(): null { return null },\n      getWorldWidth(): number { return 0 }\n    }\n  }\n\n  setMap(): void { return }\n\n  addListener() {\n    return {\n      remove() { return }\n    }\n  }\n\n  bindTo(): void { return }\n\n  get(): void { return }\n\n  notify(): void { return }\n\n  set(): void { return }\n  setValues(): void { return }\n  unbind(): void { return }\n  unbindAll(): void { return }\n\n  setupStyles(): void {\n    if (this.styles.length > 0) {\n      return\n    }\n\n    for (let i = 0; i < this.imageSizes.length; i++) {\n      this.styles.push({\n        url: `${this.imagePath + (i + 1)}.${this.imageExtension}`,\n        height: this.imageSizes[i] || 0,\n        width: this.imageSizes[i] || 0,\n      })\n    }\n  }\n\n  fitMapToMarkers(): void {\n    const markers = this.getMarkers()\n\n    const bounds = new google.maps.LatLngBounds()\n\n    for (const marker of markers) {\n      const position = marker.getPosition()\n\n      if (position) {\n        bounds.extend(position)\n      }\n    }\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    if (map !== null && 'fitBounds' in map) {\n      map.fitBounds(bounds)\n    }\n\n  }\n\n  getGridSize(): number {\n    return this.gridSize\n  }\n\n  setGridSize(gridSize: number) {\n    this.gridSize = gridSize\n  }\n\n  getMinimumClusterSize(): number {\n    return this.minClusterSize\n  }\n\n  setMinimumClusterSize(minimumClusterSize: number) {\n    this.minClusterSize = minimumClusterSize\n  }\n\n  getMaxZoom(): number | null {\n    return this.maxZoom\n  }\n\n  setMaxZoom(maxZoom: number) {\n    this.maxZoom = maxZoom\n  }\n\n  getStyles(): ClusterIconStyle[] {\n    return this.styles\n  }\n\n  setStyles(styles: ClusterIconStyle[]) {\n    this.styles = styles\n  }\n\n  getTitle(): string {\n    return this.title\n  }\n\n  setTitle(title: string) {\n    this.title = title\n  }\n\n  getZoomOnClick(): boolean {\n    return this.zoomOnClick\n  }\n\n  setZoomOnClick(zoomOnClick: boolean) {\n    this.zoomOnClick = zoomOnClick\n  }\n\n  getAverageCenter(): boolean {\n    return this.averageCenter\n  }\n\n  setAverageCenter(averageCenter: boolean) {\n    this.averageCenter = averageCenter\n  }\n\n  getIgnoreHidden(): boolean {\n    return this.ignoreHidden\n  }\n\n  setIgnoreHidden(ignoreHidden: boolean) {\n    this.ignoreHidden = ignoreHidden\n  }\n\n  getEnableRetinaIcons(): boolean {\n    return this.enableRetinaIcons\n  }\n\n  setEnableRetinaIcons(enableRetinaIcons: boolean) {\n    this.enableRetinaIcons = enableRetinaIcons\n  }\n\n  getImageExtension(): string {\n    return this.imageExtension\n  }\n\n  setImageExtension(imageExtension: string) {\n    this.imageExtension = imageExtension\n  }\n\n  getImagePath(): string {\n    return this.imagePath\n  }\n\n  setImagePath(imagePath: string) {\n    this.imagePath = imagePath\n  }\n\n  getImageSizes(): number[] {\n    return this.imageSizes\n  }\n\n  setImageSizes(imageSizes: number[]) {\n    this.imageSizes = imageSizes\n  }\n\n  getCalculator(): TCalculator {\n    return this.calculator\n  }\n\n  setCalculator(calculator: TCalculator) {\n    this.calculator = calculator\n  }\n\n  getBatchSizeIE(): number {\n    return this.batchSizeIE\n  }\n\n  setBatchSizeIE(batchSizeIE: number) {\n    this.batchSizeIE = batchSizeIE\n  }\n\n  getClusterClass(): string {\n    return this.clusterClass\n  }\n\n  setClusterClass(clusterClass: string) {\n    this.clusterClass = clusterClass\n  }\n\n  getMarkers(): MarkerExtended[] {\n    return this.markers\n  }\n\n  getTotalMarkers(): number {\n    return this.markers.length\n  }\n\n  getClusters(): Cluster[] {\n    return this.clusters\n  }\n\n  getTotalClusters(): number {\n    return this.clusters.length\n  }\n\n  addMarker(marker: MarkerExtended, optNoDraw: boolean) {\n    this.pushMarkerTo(marker)\n\n    if (!optNoDraw) {\n      this.redraw()\n    }\n  }\n\n  addMarkers(markers: MarkerExtended[], optNoDraw: boolean) {\n    for (const key in markers) {\n      if (Object.prototype.hasOwnProperty.call(markers, key)) {\n        const marker = markers[key]\n\n        if (marker) {\n          this.pushMarkerTo(marker)\n        }\n      }\n    }\n\n    if (!optNoDraw) {\n      this.redraw()\n    }\n  }\n\n  pushMarkerTo(marker: MarkerExtended) {\n    // If the marker is draggable add a listener so we can update the clusters on the dragend:\n    if (marker.getDraggable()) {\n      google.maps.event.addListener(marker, 'dragend', () => {\n        if (this.ready) {\n          marker.isAdded = false\n\n          this.repaint()\n        }\n      })\n    }\n\n    marker.isAdded = false\n\n    this.markers.push(marker)\n  }\n\n  removeMarker_(marker: MarkerExtended): boolean {\n    let index = -1\n\n    if (this.markers.indexOf) {\n      index = this.markers.indexOf(marker)\n    } else {\n      for (let i = 0; i < this.markers.length; i++) {\n        if (marker === this.markers[i]) {\n          index = i\n\n          break\n        }\n      }\n    }\n\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false\n    }\n\n    marker.setMap(null)\n\n    this.markers.splice(index, 1) // Remove the marker from the list of managed markers\n\n    return true\n  }\n\n  removeMarker(marker: MarkerExtended, optNoDraw: boolean): boolean {\n    const removed = this.removeMarker_(marker)\n\n    if (!optNoDraw && removed) {\n      this.repaint()\n    }\n\n    return removed\n  }\n\n  removeMarkers(markers: MarkerExtended[], optNoDraw: boolean): boolean {\n    let removed = false\n\n    for (const marker of markers) {\n      removed = removed || this.removeMarker_(marker)\n    }\n\n    if (!optNoDraw && removed) {\n      this.repaint()\n    }\n\n    return removed\n  }\n\n  clearMarkers() {\n    this.resetViewport(true)\n\n    this.markers = []\n  }\n\n  repaint() {\n    const oldClusters = this.clusters.slice()\n\n    this.clusters = []\n\n    this.resetViewport(false)\n\n    this.redraw()\n\n    // Remove the old clusters.\n    // Do it in a timeout to prevent blinking effect.\n    setTimeout(function timeout() {\n      for (const oldCluster of oldClusters) {\n        oldCluster.remove()\n      }\n    }, 0)\n  }\n\n  getExtendedBounds(bounds: google.maps.LatLngBounds): google.maps.LatLngBounds {\n    const projection = (this as unknown as google.maps.OverlayView).getProjection()\n\n    // Convert the points to pixels and the extend out by the grid size.\n    const trPix = projection.fromLatLngToDivPixel(\n      // Turn the bounds into latlng.\n      new google.maps.LatLng(bounds.getNorthEast().lat(), bounds.getNorthEast().lng())\n    )\n\n    if (trPix !== null) {\n      trPix.x += this.gridSize\n      trPix.y -= this.gridSize\n    }\n\n    const blPix = projection.fromLatLngToDivPixel(\n      // Turn the bounds into latlng.\n      new google.maps.LatLng(bounds.getSouthWest().lat(), bounds.getSouthWest().lng())\n    )\n\n    if (blPix !== null) {\n      blPix.x -= this.gridSize\n      blPix.y += this.gridSize\n    }\n\n\n    // Extend the bounds to contain the new bounds.\n    if (trPix !== null) {\n      // Convert the pixel points back to LatLng nw\n      const point1 = projection.fromDivPixelToLatLng(trPix)\n\n      if (point1 !== null) {\n        bounds.extend(point1)\n      }\n    }\n\n    if (blPix !== null) {\n      // Convert the pixel points back to LatLng sw\n      const point2 =  projection.fromDivPixelToLatLng(blPix)\n\n      if (point2 !== null) {\n        bounds.extend(\n          point2\n        )\n      }\n    }\n\n\n    return bounds\n  }\n\n  redraw() {\n    // Redraws all the clusters.\n    this.createClusters(0)\n  }\n\n  resetViewport(optHide: boolean) {\n    // Remove all the clusters\n    for (const cluster of this.clusters) {\n      cluster.remove()\n    }\n\n    this.clusters = []\n\n    // Reset the markers to not be added and to be removed from the map.\n    for (const marker of this.markers) {\n      marker.isAdded = false\n\n      if (optHide) {\n        marker.setMap(null)\n      }\n    }\n  }\n\n  distanceBetweenPoints(p1: google.maps.LatLng, p2: google.maps.LatLng): number {\n    const R = 6371 // Radius of the Earth in km\n\n    const dLat = ((p2.lat() - p1.lat()) * Math.PI) / 180\n    const dLon = ((p2.lng() - p1.lng()) * Math.PI) / 180\n\n    const a =\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n      Math.cos((p1.lat() * Math.PI) / 180) *\n        Math.cos((p2.lat() * Math.PI) / 180) *\n        Math.sin(dLon / 2) *\n        Math.sin(dLon / 2)\n\n    return R * (2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)))\n  }\n\n  isMarkerInBounds(marker: MarkerExtended, bounds: google.maps.LatLngBounds): boolean {\n    const position = marker.getPosition()\n\n    if (position) {\n      return bounds.contains(position)\n    }\n\n    return false\n  }\n\n  addToClosestCluster(marker: MarkerExtended) {\n    let cluster\n\n    let distance = 40000 // Some large number\n\n    let clusterToAddTo = null\n\n    for (const clusterElement of this.clusters) {\n      cluster = clusterElement\n\n      const center = cluster.getCenter()\n\n      const position = marker.getPosition()\n\n      if (center && position) {\n        const d = this.distanceBetweenPoints(center, position)\n\n        if (d < distance) {\n          distance = d\n\n          clusterToAddTo = cluster\n        }\n      }\n    }\n\n    if (clusterToAddTo && clusterToAddTo.isMarkerInClusterBounds(marker)) {\n      clusterToAddTo.addMarker(marker)\n    } else {\n      cluster = new Cluster(this)\n\n      cluster.addMarker(marker)\n\n      this.clusters.push(cluster)\n    }\n  }\n\n  createClusters(iFirst: number) {\n    if (!this.ready) {\n      return\n    }\n\n    // Cancel previous batch processing if we're working on the first batch:\n    if (iFirst === 0) {\n      /**\n       * This event is fired when the <code>Clusterer</code> begins\n       *  clustering markers.\n       * @name Clusterer#clusteringbegin\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringbegin', this)\n\n      if (this.timerRefStatic !== null) {\n        window.clearTimeout(this.timerRefStatic)\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        delete this.timerRefStatic\n      }\n    }\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    const bounds = map !== null && 'getBounds' in map ? map.getBounds() : null\n\n    const zoom =  map?.getZoom() || 0\n    // Get our current map view bounds.\n    // Create a new bounds object so we don't affect the map.\n    //\n    // See Comments 9 & 11 on Issue 3651 relating to this workaround for a Google Maps bug:\n    const mapBounds = zoom > 3\n        ? new google.maps.LatLngBounds(\n            bounds?.getSouthWest(),\n            bounds?.getNorthEast()\n          )\n        : new google.maps.LatLngBounds(\n            new google.maps.LatLng(85.02070771743472, -178.48388434375),\n            new google.maps.LatLng(-85.08136444384544, 178.00048865625)\n          )\n\n    const extendedMapBounds = this.getExtendedBounds(mapBounds)\n\n    const iLast = Math.min(iFirst + this.batchSize, this.markers.length)\n\n    for (let i = iFirst; i < iLast; i++) {\n      const marker = this.markers[i]\n\n      if (marker && !marker.isAdded && this.isMarkerInBounds(marker, extendedMapBounds) && (!this.ignoreHidden || (this.ignoreHidden && marker.getVisible()))) {\n        this.addToClosestCluster(marker)\n      }\n    }\n\n    if (iLast < this.markers.length) {\n      this.timerRefStatic = window.setTimeout(\n        () => {\n          this.createClusters(iLast)\n        },\n        0\n      )\n    } else {\n      this.timerRefStatic = null\n\n      /**\n       * This event is fired when the <code>Clusterer</code> stops\n       *  clustering markers.\n       * @name Clusterer#clusteringend\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringend', this)\n\n      for (const cluster of this.clusters) {\n        cluster.updateIcon()\n      }\n    }\n  }\n\n  extend<A extends typeof Clusterer | typeof ClusterIcon>(obj1: A, obj2: typeof google.maps.OverlayView): A {\n    return function applyExtend(this: A, object: typeof google.maps.OverlayView): A {\n      for (const property in object.prototype) {\n\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const prop = property as keyof google.maps.OverlayView & (string & {})\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.prototype[prop] = object.prototype[prop]\n      }\n\n      return this\n    }.apply<A, [typeof google.maps.OverlayView], A>(obj1, [obj2])\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAMA,QAAA,WAAA,kBAAA,YAAA;QA2BE,SAAY,WAAA,CAAA,OAAgB,EAAE,MAA0B,EAAA;IACtD,QAAA,OAAO,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAEnE,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;IAEtB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,eAAe,EAAE,CAAA;IAErE,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAA;IAEtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IAEpB,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;IAEvB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;IAEf,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAEhB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;IAEpB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;IAEjC,QAAA,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;IAEb,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;IACf,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAEd,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YACxB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAExB,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAA;IACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;IAClB,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAA;IAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAA;IACxB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;IACzB,QAAA,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAA;IAEpC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;IAE/B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;IAC/B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;IACjC,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEnB,IAA2C,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;YAErE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACzD;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;IACE,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAA;SACtD,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;IACE,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;IAE/B,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAA;SACnC,CAAA;QAED,WAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAY,EAAA;IAClB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;IAEhC,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC/B,IAAM,iBAAe,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAA;IAEnD;;;;;IAKG;IACH,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAe,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IACjE,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAe,EAAE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;;;IAIxE,YAAA,IAAI,iBAAe,CAAC,cAAc,EAAE,EAAE;;IAEpC,gBAAA,IAAM,SAAO,GAAG,iBAAe,CAAC,UAAU,EAAE,CAAA;oBAE5C,IAAM,QAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAA;IAEvC,gBAAA,IAAM,GAAG,GAAI,iBAAsD,CAAC,MAAM,EAAE,CAAA;oBAE5E,IAAI,GAAG,KAAK,IAAI,IAAI,WAAW,IAAI,GAAG,EAAE;IACtC,oBAAA,GAAG,CAAC,SAAS,CAAC,QAAM,CAAC,CAAA;qBACtB;;IAID,gBAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;IAC/B,oBAAA,IAAM,GAAG,GAAI,iBAAsD,CAAC,MAAM,EAAE,CAAA;IAE5E,oBAAA,IAAI,GAAG,KAAK,IAAI,EAAE;IAChB,wBAAA,IAAI,WAAW,IAAI,GAAG,EAAE;IACtB,4BAAA,GAAG,CAAC,SAAS,CAAC,QAAM,CAAC,CAAA;6BACtB;4BAED,IAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;;4BAG/B,IACE,SAAO,KAAK,IAAI;gCAChB,IAAI,GAAG,SAAO,EACd;IACA,4BAAA,GAAG,CAAC,OAAO,CAAC,SAAO,GAAG,CAAC,CAAC,CAAA;6BACzB;yBACF;qBACF,EAAE,GAAG,CAAC,CAAA;iBACR;;IAGD,YAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAA;IAEzB,YAAA,IAAI,KAAK,CAAC,eAAe,EAAE;oBACzB,KAAK,CAAC,eAAe,EAAE,CAAA;iBACxB;aACF;SACF,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;IACE;;;;;IAKG;YACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAC3B,WAAW,EACX,IAAI,CAAC,OAAO,CACb,CAAA;SACF,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;IACE;;;;;IAKG;YACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAC3B,UAAU,EACV,IAAI,CAAC,OAAO,CACb,CAAA;SACF,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;;YACE,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAExC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;IAEnC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI,CAAC,IAAI,EAAE,CAAA;aACZ;IAEA,QAAA,CAAA,EAAA,GAAC,IAA2C,CAAC,QAAQ,EAAE,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAElG,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;IAEjE,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;;IAEhB,YAAA,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CACxD,GAAG,EACH,gBAAgB,EAChB,IAAI,CAAC,eAAe,CACrB,CAAA;gBAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;gBAExD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;gBAEhD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;gBAExD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;aACvD;SACF,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;YACE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;gBACnC,IAAI,CAAC,IAAI,EAAE,CAAA;IAEX,YAAA,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;oBACvC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;iBAC7D;gBAED,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;gBAE3D,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;gBAEnD,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;gBAE3D,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;gBAEzD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAEzC,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;IACzB,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAEjC,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;iBACpB;IAED,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;aAChB;SACF,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;IACE,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;gBACpD,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAE9C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,EAAA,CAAA,MAAA,CAAG,GAAG,CAAC,CAAC,OAAI,GAAG,GAAG,CAAA;gBACtD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,EAAA,CAAA,MAAA,CAAG,GAAG,CAAC,CAAC,OAAI,GAAG,GAAG,CAAA;aACxD;SACF,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;IACE,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAA;aAChC;IAED,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;SACrB,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;;YACE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;IAC3B,YAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI;IACnC,gBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW;oBACtC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,GAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;;gBAGlF,IAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAE7C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAA,CAAA,EAAA,GAAA,EAAE,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,KAAI,GAAG,EAAE,EAAE,CAAC,CAAA;gBACrE,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAA,CAAA,EAAA,GAAA,EAAE,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,KAAI,GAAG,EAAE,EAAE,CAAC,CAAA;gBAErE,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAE9C,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;gBACnC,IAAI,CAAC,GAAG,CAAE,YAAY,CAAC,OAAO,EAAE,4CAA6C,CAAA,MAAA,CAAA,GAAG,KAAK,IAAI,GAAG,UAAG,GAAG,CAAC,CAAC,EAAI,IAAA,CAAA,GAAG,GAAG,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,GAAG,KAAK,IAAI,GAAG,UAAG,GAAG,CAAC,CAAC,EAAA,IAAA,CAAI,GAAG,GAAG,EAAA,WAAA,CAAA,CAAA,MAAA,CAAY,IAAI,CAAC,KAAK,EAAA,cAAA,CAAA,CAAA,MAAA,CAAe,IAAI,CAAC,MAAM,EAAM,MAAA,CAAA,CAAC,CAAA;gBAEjN,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IAEzC,YAAA,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAA;IAClB,YAAA,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;IAClB,YAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;IACtB,YAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;gBACxB,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,2BAA4B,CAAA,MAAA,CAAA,OAAO,EAAa,YAAA,CAAA,CAAA,MAAA,CAAA,OAAO,EAAI,IAAA,CAAA,CAAC,CAAA;gBAEtF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,iBAAiB,EAAE;oBAClD,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,QAAS,CAAA,MAAA,CAAA,OAAO,EAAQ,OAAA,CAAA,CAAA,MAAA,CAAA,OAAO,GAAG,IAAI,CAAC,KAAK,EAAA,OAAA,CAAA,CAAA,MAAA,CAC3D,OAAO,GAAG,IAAI,CAAC,MAAM,EAAA,KAAA,CAAA,CAAA,MAAA,CACjB,OAAO,EAAA,GAAA,CAAG,CAAA;iBACjB;gBAED,IAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;gBAE7C,OAAO,CAAE,YAAY,CAAC,OAAO,EAAE,mCAA4B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,wBAAc,IAAI,CAAC,SAAS,EAAA,eAAA,CAAA,CAAA,MAAA,CAAgB,IAAI,CAAC,QAAQ,EAAoB,mBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,UAAU,EAAkB,iBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,UAAU,EAAA,eAAA,CAAA,CAAA,MAAA,CAAgB,IAAI,CAAC,SAAS,EAAA,qBAAA,CAAA,CAAA,MAAA,CAAsB,IAAI,CAAC,cAAc,EAAgC,+BAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,KAAK,EAAoB,mBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,MAAM,EAAI,IAAA,CAAA,CAAC,CAAA;IAE9X,YAAA,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,0CAAE,IAAI;oBAAE,OAAO,CAAC,SAAS,GAAG,EAAG,CAAA,MAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAE,CAAA;IAC7D,YAAA,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,0CAAE,IAAI;oBAAE,OAAO,CAAC,SAAS,GAAG,EAAG,CAAA,MAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAE,CAAA;IAE7D,YAAA,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE,CAAA;IAEvB,YAAA,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;IACzB,YAAA,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;IAE7B,YAAA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAA;gBAEzB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAA;aAC5B;IAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;SACpB,CAAA;QAED,WAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,IAAqB,EAAA;IAC5B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAEhB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,SAAS,EAAE,CAAA;IAEtD,QAAA,IAAM,KAAK,GACT,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAElE,IAAI,KAAK,EAAE;IACT,YAAA,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;IACpB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;IAC1B,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;IAExB,YAAA,IAAI,KAAK,CAAC,SAAS,EAAE;IACnB,gBAAA,IAAI,CAAC,SAAS,GAAG,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,gBAAgB,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,KAAK,CAAC,SAAS,CAAE,CAAA;iBAC/D;IAED,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC5C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;gBAEvE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,OAAO,CAAA;gBAE3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAA;gBAEpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,MAAM,CAAA;gBAEpD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,MAAM,CAAA;gBAE5C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,QAAQ,CAAA;gBAE5C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,kBAAkB,CAAA;gBAExD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAA;aAC5D;SACF,CAAA;QAED,WAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAA0B,EAAA;IAClC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;SACrB,CAAA;QAED,WAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,MAA0B,EAAA;YACzC,IAAM,GAAG,GAAI,IAA2C,CAAC,aAAa,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;IAErG,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;gBAChB,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;gBAE3B,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;aAC5B;IAED,QAAA,OAAO,GAAG,CAAA;SACX,CAAA;QACH,OAAC,WAAA,CAAA;IAAD,CAAC,EAAA;;ICjXD;AAQA,QAAA,OAAA,kBAAA,YAAA;IAWE,IAAA,SAAA,OAAA,CAAY,eAA0B,EAAA;IACpC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAA;YAEtC,IAAI,CAAC,GAAG,GAAI,IAAI,CAAC,eAAsD,CAAC,MAAM,EAAE,CAAA;YAEhF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAA;YAElD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAA;YAElE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAA;IAE5D,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;IAEjB,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;IAEvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;IAElB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAA;YAE1E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACjE;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;SAC3B,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;YACE,OAAO,IAAI,CAAC,OAAO,CAAA;SACpB,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;YACE,OAAO,IAAI,CAAC,MAAM,CAAA;SACnB,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;YACE,OAAO,IAAI,CAAC,GAAG,CAAA;SAChB,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;YACE,OAAO,IAAI,CAAC,eAAe,CAAA;SAC5B,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;IACE,QAAA,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;IAErE,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;YAEjC,KAAqB,IAAA,EAAA,GAAA,CAAO,EAAP,SAAO,GAAA,OAAA,EAAP,qBAAO,EAAP,EAAA,EAAO,EAAE;IAAzB,YAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;IACf,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;gBAErC,IAAI,QAAQ,EAAE;IACZ,gBAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;iBACxB;aACF;IAED,QAAA,OAAO,MAAM,CAAA;SACd,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;IACG,QAAA,IAAI,CAAC,WAAkD,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAErE,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;;;YAIjB,OAAO,IAAI,CAAC,OAAO,CAAA;SACpB,CAAA;QAED,OAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAAsB,EAAA;;IAC9B,QAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;IACrC,YAAA,OAAO,KAAK,CAAA;aACb;IAED,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IAChB,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;gBAErC,IAAI,QAAQ,EAAE;IACZ,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAA;oBAEtB,IAAI,CAAC,eAAe,EAAE,CAAA;iBACvB;aACF;iBAAM;IACL,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;IACtB,gBAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;oBAErC,IAAI,QAAQ,EAAE;wBACZ,IAAM,QAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;wBAEtC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAClC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,QAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,QAAM,EAC5D,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,QAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,QAAM,CAC7D,CAAA;wBAED,IAAI,CAAC,eAAe,EAAE,CAAA;qBACvB;iBACF;aACF;IAED,QAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAA;IAErB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAEzB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;YAElC,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAA;YAEjD,IAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE,CAAA;IAEhC,QAAA,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAG,OAAO,EAAE;;gBAErE,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE;IAChC,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;iBACxB;aACF;IAAM,aAAA,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;;gBAEvC,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE;IAChC,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;iBACxB;aACF;IAAM,aAAA,IAAI,MAAM,KAAK,IAAI,CAAC,cAAc,EAAE;;gBAEzC,KAA4B,IAAA,EAAA,GAAA,CAAY,EAAZ,EAAA,GAAA,IAAI,CAAC,OAAO,EAAZ,EAAY,GAAA,EAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAE;IAArC,gBAAA,IAAM,aAAa,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;IACtB,gBAAA,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;iBAC3B;aACF;iBAAM;IACL,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aACpB;IAED,QAAA,OAAO,IAAI,CAAA;SACZ,CAAA;QAED,OAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,MAAsB,EAAA;IAC5C,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;IACxB,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;gBAErC,IAAI,QAAQ,EAAE;oBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;iBACtC;aACF;IAED,QAAA,OAAO,KAAK,CAAA;SACb,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;YACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAClD,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CACvD,CAAA;SACF,CAAA;IAED,IAAA,OAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;;IACE,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;YAElC,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAA;YAEjD,IAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE,CAAA;IAEhC,QAAA,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAG,OAAO,EAAE;IACrE,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;gBAEvB,OAAM;aACP;IAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;;IAEhC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;gBAEvB,OAAM;aACP;IAED,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;aACxC;YAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CACvB,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAC5F,CAAA;IAED,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;SACxB,CAAA;QAED,OAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,MAAsB,EAAA;IACzC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACzB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;aACrC;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;IAC9B,gBAAA,OAAO,IAAI,CAAA;iBACZ;aACF;IAED,QAAA,OAAO,KAAK,CAAA;SACb,CAAA;QACH,OAAC,OAAA,CAAA;IAAD,CAAC,EAAA;;IC9ND;IACA;IAYA;;;IAGG;IACH,SAAS,UAAU,CACjB,OAAyB,EACzB,SAAiB,EAAA;IAEjB,IAAA,IAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAA;QAE5B,IAAM,cAAc,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAA;QAE9C,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;QAEjD,OAAO;IACL,QAAA,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE;IACtB,QAAA,KAAK,EAAA,KAAA;IACL,QAAA,KAAK,EAAE,EAAE;SACV,CAAA;IACH,CAAC;IAED,IAAM,UAAU,GAAG,IAAI,CAAA;IAEvB,IAAM,aAAa,GAAG,GAAG,CAAA;IAEzB,IAAM,UAAU,GACd,wFAAwF,CAAA;IAE1F,IAAM,eAAe,GAAG,KAAK,CAAA;IAE7B,IAAM,WAAW,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;IAExC,IAAM,eAAe,GAAG,SAAS,CAAA;AAEjC,QAAA,SAAA,kBAAA,YAAA;IAwBE,IAAA,SAAA,SAAA,CACE,GAAoB,EACpB,UAAiC,EACjC,UAAiC,EAAA;IADjC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAiC,GAAA,EAAA,CAAA,EAAA;IACjC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAiC,GAAA,EAAA,CAAA,EAAA;YAEjC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEhC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAE/C,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;IACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;IAClB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;IACnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;IACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;YAClB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAA;YACzC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,kBAAkB,IAAI,CAAC,CAAA;YACxD,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,IAAI,IAAI,CAAA;YACzC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,EAAE,CAAA;YAErC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAA;IAEnC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;IAEvB,QAAA,IAAI,UAAU,CAAC,WAAW,KAAK,SAAS,EAAE;IACxC,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAA;aAC1C;IAED,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;IAE1B,QAAA,IAAI,UAAU,CAAC,aAAa,KAAK,SAAS,EAAE;IAC1C,YAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAA;aAC9C;IAED,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;IAEzB,QAAA,IAAI,UAAU,CAAC,YAAY,KAAK,SAAS,EAAE;IACzC,YAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAA;aAC5C;IAED,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;IAE9B,QAAA,IAAI,UAAU,CAAC,iBAAiB,KAAK,SAAS,EAAE;IAC9C,YAAA,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAA;aACtD;YACD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAA;YAEnD,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,IAAI,eAAe,CAAA;YAElE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,WAAW,CAAA;YAEtD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,UAAU,CAAA;YAErD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAA;YAEnD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,aAAa,CAAA;YAE1D,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,eAAe,CAAA;IAE9D,QAAA,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;;IAE5D,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAA;aAClC;IAED,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAE1B,IAAI,CAAC,WAAW,EAAE,CAAA;IAElB,QAAA,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAEjC,QAAA,IAA2C,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;SACzD;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;;IACE,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;;;;;;IAOzB,QAAA,IACE,CAAA,CAAC,EAAA,GAAA,IAA2C,CAAC,MAAM,EAAE,0CAAE,OAAO,EAAE,OAAO,IAA2C,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvI,YAAA,CAAA,MAAC,IAA2C,CAAC,MAAM,EAAE,0CAAE,OAAO,EAAE,MAAM,IAA2C,CAAC,GAAG,CAAC,SAAS,CAAC,EAChI;gBACA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;aACxC;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;YACE,IAAI,CAAC,MAAM,EAAE,CAAA;SACd,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;IACE,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;IAEjE,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;IAEpB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;YAEjB,IAAI,CAAC,OAAO,EAAE,CAAA;IAEd,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;;gBAEhB,IAAI,CAAC,SAAS,GAAG;IACf,gBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC3B,GAAG,EACH,cAAc,EACd,IAAI,CAAC,aAAa,CACnB;IACD,gBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC3B,GAAG,EACH,MAAM,EACN,IAAI,CAAC,MAAM,CACZ;iBACF,CAAA;aACF;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;;YAEE,KAAqB,IAAA,EAAA,GAAA,CAAY,EAAZ,EAAA,GAAA,IAAI,CAAC,OAAO,EAAZ,EAAY,GAAA,EAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAE;IAA9B,YAAA,IAAM,MAAM,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBACf,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,SAAS,EAAE;IACtC,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;iBAC9B;aACF;;YAGD,KAAsB,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;IAAhC,YAAA,IAAM,OAAO,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBAChB,OAAO,CAAC,MAAM,EAAE,CAAA;aACjB;IAED,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;;YAGlB,KAAuB,IAAA,EAAA,GAAA,CAAc,EAAd,EAAA,GAAA,IAAI,CAAC,SAAS,EAAd,EAAc,GAAA,EAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAE;IAAlC,YAAA,IAAM,QAAQ,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBACjB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;aAC3C;IAED,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;IAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;IAErB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;SACnB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA,EAAe,OAAM,EAAE,CAAA;IAEvB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAiB,EAAA,OAAO,IAAI,CAAA,EAAE,CAAA;IAE9B,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAmB,EAAA,OAAO,IAAI,CAAA,EAAE,CAAA;IAEhC,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;YACE,OAAO;IACL,YAAA,0BAA0B,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAE;IAClD,YAAA,oBAAoB,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAC;IAC3C,YAAA,0BAA0B,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAC;IACjD,YAAA,oBAAoB,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAC;IAC3C,YAAA,gBAAgB,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAE;IACxC,YAAA,aAAa,EAAa,YAAA,EAAA,OAAO,CAAC,CAAA,EAAE;aACrC,CAAA;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;IAEzB,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;YACE,OAAO;gBACL,MAAM,EAAA,YAAA,EAAK,OAAM,EAAE;aACpB,CAAA;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;IAEzB,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA,EAAc,OAAM,EAAE,CAAA;IAEtB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;IAEzB,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA,EAAc,OAAM,EAAE,CAAA;IACtB,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA,EAAoB,OAAM,EAAE,CAAA;IAC5B,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;IACzB,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA,EAAoB,OAAM,EAAE,CAAA;IAE5B,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;YACE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,OAAM;aACP;IAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC/C,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACf,gBAAA,GAAG,EAAE,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,IAAI,CAAC,cAAc,CAAE;oBACzD,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC/B,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/B,aAAA,CAAC,CAAA;aACH;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;IACE,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;YAEjC,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAA;YAE7C,KAAqB,IAAA,EAAA,GAAA,CAAO,EAAP,SAAO,GAAA,OAAA,EAAP,qBAAO,EAAP,EAAA,EAAO,EAAE;IAAzB,YAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;IACf,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;gBAErC,IAAI,QAAQ,EAAE;IACZ,gBAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;iBACxB;aACF;IAED,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;YAEjE,IAAI,GAAG,KAAK,IAAI,IAAI,WAAW,IAAI,GAAG,EAAE;IACtC,YAAA,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;aACtB;SAEF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;YACE,OAAO,IAAI,CAAC,QAAQ,CAAA;SACrB,CAAA;QAED,SAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAAgB,EAAA;IAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;SACzB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,YAAA;YACE,OAAO,IAAI,CAAC,cAAc,CAAA;SAC3B,CAAA;QAED,SAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,kBAA0B,EAAA;IAC9C,QAAA,IAAI,CAAC,cAAc,GAAG,kBAAkB,CAAA;SACzC,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;YACE,OAAO,IAAI,CAAC,OAAO,CAAA;SACpB,CAAA;QAED,SAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,OAAe,EAAA;IACxB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;SACvB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;YACE,OAAO,IAAI,CAAC,MAAM,CAAA;SACnB,CAAA;QAED,SAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAA0B,EAAA;IAClC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;SACrB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;YACE,OAAO,IAAI,CAAC,KAAK,CAAA;SAClB,CAAA;QAED,SAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,KAAa,EAAA;IACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;SACnB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;YACE,OAAO,IAAI,CAAC,WAAW,CAAA;SACxB,CAAA;QAED,SAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAoB,EAAA;IACjC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;SAC/B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;YACE,OAAO,IAAI,CAAC,aAAa,CAAA;SAC1B,CAAA;QAED,SAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,aAAsB,EAAA;IACrC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;SACnC,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;YACE,OAAO,IAAI,CAAC,YAAY,CAAA;SACzB,CAAA;QAED,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAqB,EAAA;IACnC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;SACjC,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;YACE,OAAO,IAAI,CAAC,iBAAiB,CAAA;SAC9B,CAAA;QAED,SAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,iBAA0B,EAAA;IAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAA;SAC3C,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;YACE,OAAO,IAAI,CAAC,cAAc,CAAA;SAC3B,CAAA;QAED,SAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,cAAsB,EAAA;IACtC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;SACrC,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;YACE,OAAO,IAAI,CAAC,SAAS,CAAA;SACtB,CAAA;QAED,SAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,SAAiB,EAAA;IAC5B,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;SAC3B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;YACE,OAAO,IAAI,CAAC,UAAU,CAAA;SACvB,CAAA;QAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAoB,EAAA;IAChC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;SAC7B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;YACE,OAAO,IAAI,CAAC,UAAU,CAAA;SACvB,CAAA;QAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAuB,EAAA;IACnC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;SAC7B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;YACE,OAAO,IAAI,CAAC,WAAW,CAAA;SACxB,CAAA;QAED,SAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAmB,EAAA;IAChC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;SAC/B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;YACE,OAAO,IAAI,CAAC,YAAY,CAAA;SACzB,CAAA;QAED,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAoB,EAAA;IAClC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;SACjC,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;YACE,OAAO,IAAI,CAAC,OAAO,CAAA;SACpB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;IACE,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;SAC3B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;YACE,OAAO,IAAI,CAAC,QAAQ,CAAA;SACrB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;IACE,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;SAC5B,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,MAAsB,EAAE,SAAkB,EAAA;IAClD,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;YAEzB,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,MAAM,EAAE,CAAA;aACd;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,OAAyB,EAAE,SAAkB,EAAA;IACtD,QAAA,KAAK,IAAM,GAAG,IAAI,OAAO,EAAE;IACzB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;IACtD,gBAAA,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;oBAE3B,IAAI,MAAM,EAAE;IACV,oBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;qBAC1B;iBACF;aACF;YAED,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,MAAM,EAAE,CAAA;aACd;SACF,CAAA;QAED,SAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,MAAsB,EAAA;YAAnC,IAeC,KAAA,GAAA,IAAA,CAAA;;IAbC,QAAA,IAAI,MAAM,CAAC,YAAY,EAAE,EAAE;gBACzB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,YAAA;IAC/C,gBAAA,IAAI,KAAI,CAAC,KAAK,EAAE;IACd,oBAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAA;wBAEtB,KAAI,CAAC,OAAO,EAAE,CAAA;qBACf;IACH,aAAC,CAAC,CAAA;aACH;IAED,QAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAA;IAEtB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC1B,CAAA;QAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,MAAsB,EAAA;IAClC,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAA;IAEd,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;aACrC;iBAAM;IACL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;wBAC9B,KAAK,GAAG,CAAC,CAAA;wBAET,MAAK;qBACN;iBACF;aACF;IAED,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;;IAEhB,YAAA,OAAO,KAAK,CAAA;aACb;IAED,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAEnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;IAE7B,QAAA,OAAO,IAAI,CAAA;SACZ,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,MAAsB,EAAE,SAAkB,EAAA;YACrD,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IAE1C,QAAA,IAAI,CAAC,SAAS,IAAI,OAAO,EAAE;gBACzB,IAAI,CAAC,OAAO,EAAE,CAAA;aACf;IAED,QAAA,OAAO,OAAO,CAAA;SACf,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,OAAyB,EAAE,SAAkB,EAAA;YACzD,IAAI,OAAO,GAAG,KAAK,CAAA;YAEnB,KAAqB,IAAA,EAAA,GAAA,CAAO,EAAP,SAAO,GAAA,OAAA,EAAP,qBAAO,EAAP,EAAA,EAAO,EAAE;IAAzB,YAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;gBACf,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;aAChD;IAED,QAAA,IAAI,CAAC,SAAS,IAAI,OAAO,EAAE;gBACzB,IAAI,CAAC,OAAO,EAAE,CAAA;aACf;IAED,QAAA,OAAO,OAAO,CAAA;SACf,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;IACE,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAExB,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;SAClB,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,IAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;IAEzC,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;IAElB,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAEzB,IAAI,CAAC,MAAM,EAAE,CAAA;;;YAIb,UAAU,CAAC,SAAS,OAAO,GAAA;gBACzB,KAAyB,IAAA,EAAA,GAAA,CAAW,EAAX,aAAW,GAAA,WAAA,EAAX,yBAAW,EAAX,EAAA,EAAW,EAAE;IAAjC,gBAAA,IAAM,UAAU,GAAA,aAAA,CAAA,EAAA,CAAA,CAAA;oBACnB,UAAU,CAAC,MAAM,EAAE,CAAA;iBACpB;aACF,EAAE,CAAC,CAAC,CAAA;SACN,CAAA;QAED,SAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,MAAgC,EAAA;IAChD,QAAA,IAAM,UAAU,GAAI,IAA2C,CAAC,aAAa,EAAE,CAAA;;IAG/E,QAAA,IAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB;;YAE3C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CACjF,CAAA;IAED,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;IAClB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;IACxB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;aACzB;IAED,QAAA,IAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB;;YAE3C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CACjF,CAAA;IAED,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;IAClB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;IACxB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;aACzB;;IAID,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;;gBAElB,IAAM,MAAM,GAAG,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;IAErD,YAAA,IAAI,MAAM,KAAK,IAAI,EAAE;IACnB,gBAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;iBACtB;aACF;IAED,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;;gBAElB,IAAM,MAAM,GAAI,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;IAEtD,YAAA,IAAI,MAAM,KAAK,IAAI,EAAE;IACnB,gBAAA,MAAM,CAAC,MAAM,CACX,MAAM,CACP,CAAA;iBACF;aACF;IAGD,QAAA,OAAO,MAAM,CAAA;SACd,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;;IAEE,QAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;SACvB,CAAA;QAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,OAAgB,EAAA;;YAE5B,KAAsB,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;IAAhC,YAAA,IAAM,OAAO,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBAChB,OAAO,CAAC,MAAM,EAAE,CAAA;aACjB;IAED,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;;YAGlB,KAAqB,IAAA,EAAA,GAAA,CAAY,EAAZ,EAAA,GAAA,IAAI,CAAC,OAAO,EAAZ,EAAY,GAAA,EAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAE;IAA9B,YAAA,IAAM,MAAM,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;IACf,YAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAA;gBAEtB,IAAI,OAAO,EAAE;IACX,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;iBACpB;aACF;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,UAAsB,EAAsB,EAAE,EAAsB,EAAA;IAClE,QAAA,IAAM,CAAC,GAAG,IAAI,CAAA;YAEd,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,CAAA;YACpD,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,CAAA;IAEpD,QAAA,IAAM,CAAC,GACL,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;IACvC,YAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;IAClC,gBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;IACpC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;IAClB,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;YAEtB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;SAC5D,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,MAAsB,EAAE,MAAgC,EAAA;IACvE,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YAErC,IAAI,QAAQ,EAAE;IACZ,YAAA,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;aACjC;IAED,QAAA,OAAO,KAAK,CAAA;SACb,CAAA;QAED,SAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,MAAsB,EAAA;IACxC,QAAA,IAAI,OAAO,CAAA;IAEX,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAA;YAEpB,IAAI,cAAc,GAAG,IAAI,CAAA;YAEzB,KAA6B,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;IAAvC,YAAA,IAAM,cAAc,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBACvB,OAAO,GAAG,cAAc,CAAA;IAExB,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAA;IAElC,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;IAErC,YAAA,IAAI,MAAM,IAAI,QAAQ,EAAE;oBACtB,IAAM,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAEtD,gBAAA,IAAI,CAAC,GAAG,QAAQ,EAAE;wBAChB,QAAQ,GAAG,CAAC,CAAA;wBAEZ,cAAc,GAAG,OAAO,CAAA;qBACzB;iBACF;aACF;YAED,IAAI,cAAc,IAAI,cAAc,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE;IACpE,YAAA,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;aACjC;iBAAM;IACL,YAAA,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAE3B,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IAEzB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aAC5B;SACF,CAAA;QAED,SAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,MAAc,EAAA;YAA7B,IA+EC,KAAA,GAAA,IAAA,CAAA;IA9EC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBACf,OAAM;aACP;;IAGD,QAAA,IAAI,MAAM,KAAK,CAAC,EAAE;IAChB;;;;;;IAMG;IACH,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAA;IAExD,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;IAChC,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;;;oBAIxC,OAAO,IAAI,CAAC,cAAc,CAAA;iBAC3B;aACF;IAED,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;YAEjE,IAAM,MAAM,GAAG,GAAG,KAAK,IAAI,IAAI,WAAW,IAAI,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAA;IAE1E,QAAA,IAAM,IAAI,GAAI,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,OAAO,EAAE,KAAI,CAAC,CAAA;;;;;IAKjC,QAAA,IAAM,SAAS,GAAG,IAAI,GAAG,CAAC;kBACpB,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAC1B,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,YAAY,EAAE,EACtB,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,YAAY,EAAE,CACvB;IACH,cAAE,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAC1B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,eAAe,CAAC,EAC3D,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAC5D,CAAA;YAEP,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;IAE3D,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IAEpE,QAAA,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBACnC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAE9B,YAAA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE;IACvJ,gBAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;iBACjC;aACF;YAED,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;IAC/B,YAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CACrC,YAAA;IACE,gBAAA,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;iBAC3B,EACD,CAAC,CACF,CAAA;aACF;iBAAM;IACL,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;IAE1B;;;;;;IAMG;IACH,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;gBAEtD,KAAsB,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;IAAhC,gBAAA,IAAM,OAAO,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;oBAChB,OAAO,CAAC,UAAU,EAAE,CAAA;iBACrB;aACF;SACF,CAAA;IAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAwD,IAAO,EAAE,IAAoC,EAAA;YACnG,OAAO,SAAS,WAAW,CAAU,MAAsC,EAAA;IACzE,YAAA,KAAK,IAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;;oBAGvC,IAAM,IAAI,GAAG,QAAyD,CAAA;;;IAItE,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;iBAC9C;IAED,YAAA,OAAO,IAAI,CAAA;aACZ,CAAC,KAAK,CAAyC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;SAC9D,CAAA;QACH,OAAC,SAAA,CAAA;IAAD,CAAC,EAAA;;;;;;;;;;"}